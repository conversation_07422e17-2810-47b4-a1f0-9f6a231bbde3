{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.3570.3.15", "sysDescr": "Tait Communications, Core Network, QNC11C0M1", "sysContact": "<private>", "version": null, "hardware": null, "features": null, "location": "<private>", "os": "tait-tnadmin", "type": "network", "serial": null, "icon": "tait.png"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "em1", "ifName": "em1", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "em1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "em2", "ifName": "em2", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "em2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": 10000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 65536, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 1011715843, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 1011715843, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 100612844581, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 100612844581, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "em1", "ifName": "em1", "portName": null, "ifIndex": 2, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "fullDuplex", "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "em1", "ifPhysAddress": "d8cb29c7e404", "ifLastChange": 302568219, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 2056812539, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 1826462092, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 210789724055, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 164482557533, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 44637, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 13270494, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "em2", "ifName": "em2", "portName": null, "ifIndex": 3, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": "unknown", "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "em2", "ifPhysAddress": "a8cd29d7d405", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.3570.********.*******443", "processor_index": "tnServiceCpu.1443", "processor_type": "tait-tnadmin", "processor_usage": 2, "processor_descr": "TaitNet Administration CPU pct", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.3570.********.*******3443", "processor_index": "tnServiceCpu.13443", "processor_type": "tait-tnadmin", "processor_usage": 64, "processor_descr": "TN9300 DMR Trunked CPU pct", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.3570.********.*******8443", "processor_index": "tnServiceCpu.18443", "processor_type": "tait-tnadmin", "processor_usage": 0, "processor_descr": "TN9300 Channel Group Manager CPU pct", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.3570.********.1.1.6.30443", "processor_index": "tnServiceCpu.30443", "processor_type": "tait-tnadmin", "processor_usage": 0, "processor_descr": "DMR Trunked Data API Connector CPU pct", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "1", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "system", "mempool_precision": 1024, "mempool_descr": "Physical memory", "mempool_perc": 4, "mempool_perc_oid": null, "mempool_used": 678252544, "mempool_used_oid": ".*******.********.3.1.6.1", "mempool_free": 15546658816, "mempool_free_oid": null, "mempool_total": 16224911360, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 99}, {"mempool_index": "3", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "virtual", "mempool_precision": 1024, "mempool_descr": "Virtual memory", "mempool_perc": 85, "mempool_perc_oid": null, "mempool_used": 14758694912, "mempool_used_oid": ".*******.********.3.1.6.3", "mempool_free": 2539954176, "mempool_free_oid": null, "mempool_total": 17298649088, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 95}, {"mempool_index": "6", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "buffers", "mempool_precision": 1024, "mempool_descr": "Memory buffers", "mempool_perc": 0, "mempool_perc_oid": null, "mempool_used": 20480, "mempool_used_oid": ".*******.********.3.1.6.6", "mempool_free": 16224890880, "mempool_free_oid": null, "mempool_total": 16224911360, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 0}, {"mempool_index": "7", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "cached", "mempool_precision": 1024, "mempool_descr": "Cached memory", "mempool_perc": 86, "mempool_perc_oid": null, "mempool_used": 14003875840, "mempool_used_oid": ".*******.********.3.1.6.7", "mempool_free": 2221035520, "mempool_free_oid": null, "mempool_total": 16224911360, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 0}, {"mempool_index": "8", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "shared", "mempool_precision": 1024, "mempool_descr": "Shared memory", "mempool_perc": 5, "mempool_perc_oid": null, "mempool_used": 871182336, "mempool_used_oid": ".*******.********.3.1.6.8", "mempool_free": 15353729024, "mempool_free_oid": null, "mempool_total": 16224911360, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 0}, {"mempool_index": "10", "entPhysicalIndex": null, "mempool_type": "hrstorage", "mempool_class": "swap", "mempool_precision": 1024, "mempool_descr": "Swap space", "mempool_perc": 7, "mempool_perc_oid": null, "mempool_used": 76546048, "mempool_used_oid": ".*******.********.********", "mempool_free": 997191680, "mempool_free_oid": null, "mempool_total": 1073737728, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 10}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineClientConnects.1", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site1 Nb Client Connect on DIP Line", "group": "TN9300 DipConnections DIPINT-Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********0", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineClientConnects.10", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-All_Sites Nb Client Connect on DIP Line", "group": "TN9300 DipConnections DIPINT-All_Sites", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineClientConnects.2", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site2 Nb Client Connect on DIP Line", "group": "TN9300 DipConnections DIPINT-Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 9, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineClientConnects.3", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site3 Nb Client Connect on DIP Line", "group": "TN9300 DipConnections DIPINT-Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineClientConnects.4", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site4 Nb Client Connect on DIP Line", "group": "TN9300 DipConnections DIPINT-Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineClientConnects.5", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site5 Nb Client Connect on DIP Line", "group": "TN9300 DipConnections DIPINT-Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineClientConnects.6", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site6 Nb Client Connect on DIP Line", "group": "TN9300 DipConnections DIPINT-Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineClientConnects.7", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site7 Nb Client Connect on DIP Line", "group": "TN9300 DipConnections DIPINT-Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineClientConnects.8", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site8 Nb Client Connect on DIP Line", "group": "TN9300 DipConnections DIPINT-Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpLostPackets.1", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site1 Nb NGP pkts Lost from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********0", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpLostPackets.10", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-All_Sites Nb NGP pkts Lost from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-All_Sites", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpLostPackets.2", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site2 Nb NGP pkts Lost from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpLostPackets.3", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site3 Nb NGP pkts Lost from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpLostPackets.4", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site4 Nb NGP pkts Lost from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpLostPackets.5", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site5 Nb NGP pkts Lost from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpLostPackets.6", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site6 Nb NGP pkts Lost from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpLostPackets.7", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site7 Nb NGP pkts Lost from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpLostPackets.8", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site8 Nb NGP pkts Lost from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpRxPackets.1", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site1 Nb NGP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpRxPackets.10", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-All_Sites Nb NGP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-All_Sites", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpRxPackets.2", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site2 Nb NGP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpRxPackets.3", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site3 Nb NGP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpRxPackets.4", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site4 Nb NGP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpRxPackets.5", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site5 Nb NGP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpRxPackets.6", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site6 Nb NGP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpRxPackets.7", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site7 Nb NGP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpRxPackets.8", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site8 Nb NGP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpTxPackets.1", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site1 Nb NGP pkts Tx on DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********0", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpTxPackets.10", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-All_Sites Nb NGP pkts Tx on DIP to NetGW", "group": "TN9300 DipConnections DIPINT-All_Sites", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpTxPackets.2", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site2 Nb NGP pkts Tx on DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpTxPackets.3", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site3 Nb NGP pkts Tx on DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpTxPackets.4", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site4 Nb NGP pkts Tx on DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpTxPackets.5", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site5 Nb NGP pkts Tx on DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpTxPackets.6", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site6 Nb NGP pkts Tx on DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpTxPackets.7", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site7 Nb NGP pkts Tx on DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgpTxPackets.8", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site8 Nb NGP pkts Tx on DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpJitter.1", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site1 RTT jitter from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********0", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpJitter.10", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-All_Sites RTT jitter from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-All_Sites", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpJitter.2", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site2 RTT jitter from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpJitter.3", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site3 RTT jitter from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpJitter.4", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site4 RTT jitter from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpJitter.5", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site5 RTT jitter from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpJitter.6", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site6 RTT jitter from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpJitter.7", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site7 RTT jitter from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpJitter.8", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site8 RTT jitter from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpLostPackets.1", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site1 Nb RTP pkts Lost Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpLostPackets.10", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-All_Sites Nb RTP pkts Lost Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-All_Sites", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpLostPackets.2", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site2 Nb RTP pkts Lost Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpLostPackets.3", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site3 Nb RTP pkts Lost Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpLostPackets.4", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site4 Nb RTP pkts Lost Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpLostPackets.5", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site5 Nb RTP pkts Lost Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpLostPackets.6", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site6 Nb RTP pkts Lost Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpLostPackets.7", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site7 Nb RTP pkts Lost Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpLostPackets.8", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site8 Nb RTP pkts Lost Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRtt.1", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site1 RTT from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********0", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRtt.10", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-All_Sites RTT from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-All_Sites", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRtt.2", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site2 RTT from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRtt.3", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site3 RTT from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRtt.4", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site4 RTT from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRtt.5", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site5 RTT from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRtt.6", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site6 RTT from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRtt.7", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site7 RTT from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRtt.8", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site8 RTT from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRxPackets.1", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site1 Nb RTP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********0", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRxPackets.10", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-All_Sites Nb RTP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-All_Sites", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRxPackets.2", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site2 Nb RTP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRxPackets.3", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site3 Nb RTP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRxPackets.4", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site4 Nb RTP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRxPackets.5", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site5 Nb RTP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRxPackets.6", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site6 Nb RTP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRxPackets.7", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site7 Nb RTP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpRxPackets.8", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site8 Nb RTP pkts Rx on DIP from NetGW", "group": "TN9300 DipConnections DIPINT-Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpTxPackets.1", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site1 Nb RTP pkts Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********0", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpTxPackets.10", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-All_Sites Nb RTP pkts Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-All_Sites", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpTxPackets.2", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site2 Nb RTP pkts Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpTxPackets.3", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site3 Nb RTP pkts Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpTxPackets.4", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site4 Nb RTP pkts Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpTxPackets.5", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site5 Nb RTP pkts Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpTxPackets.6", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site6 Nb RTP pkts Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpTxPackets.7", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site7 Nb RTP pkts Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineRtpTxPackets.8", "sensor_type": "tait-tnadmin", "sensor_descr": "DIPINT-Site8 Nb RTP pkts Tx from DIP to NetGW", "group": "TN9300 DipConnections DIPINT-Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued10To20.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb calls queued 10 to 20 sec since node start", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued10To20.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb calls queued 10 to 20 sec since node start", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued10To20.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb calls queued 10 to 20 sec since node start", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued10To20.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb calls queued 10 to 20 sec since node start", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued10To20.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb calls queued 10 to 20 sec since node start", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued10To20.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb calls queued 10 to 20 sec since node start", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued10To20.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb calls queued 10 to 20 sec since node start", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued10To20.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb calls queued 10 to 20 sec since node start", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued5To10.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb calls queued 5 to 10 sec since node start", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued5To10.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb calls queued 5 to 10 sec since node start", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued5To10.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb calls queued 5 to 10 sec since node start", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued5To10.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb calls queued 5 to 10 sec since node start", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued5To10.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb calls queued 5 to 10 sec since node start", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued5To10.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb calls queued 5 to 10 sec since node start", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 124, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued5To10.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb calls queued 5 to 10 sec since node start", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueued5To10.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb calls queued 5 to 10 sec since node start", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedOver20.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb calls queued above 20 sec since node start", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedOver20.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb calls queued above 20 sec since node start", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedOver20.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb calls queued above 20 sec since node start", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedOver20.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb calls queued above 20 sec since node start", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedOver20.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb calls queued above 20 sec since node start", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedOver20.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb calls queued above 20 sec since node start", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedOver20.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb calls queued above 20 sec since node start", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedOver20.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb calls queued above 20 sec since node start", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedUnder5.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb calls queued under 5 sec since node start", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28117, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedUnder5.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb calls queued under 5 sec since node start", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 79, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedUnder5.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb calls queued under 5 sec since node start", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 93, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedUnder5.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb calls queued under 5 sec since node start", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 12008, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedUnder5.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb calls queued under 5 sec since node start", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 39882, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedUnder5.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb calls queued under 5 sec since node start", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 13821, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedUnder5.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb calls queued under 5 sec since node start", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 87, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteCallsQueuedUnder5.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb calls queued under 5 sec since node start", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 79, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeAlternate.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Ch time as alternate ch since node start", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeAlternate.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Ch time as alternate ch since node start", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeAlternate.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Ch time as alternate ch since node start", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeAlternate.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Ch time as alternate ch since node start", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeAlternate.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Ch time as alternate ch since node start", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeAlternate.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Ch time as alternate ch since node start", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeAlternate.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Ch time as alternate ch since node start", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeAlternate.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Ch time as alternate ch since node start", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeControl.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Ch time Control (sec) since node start", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 22118179, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeControl.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Ch time Control (sec) since node start", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 22118173, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeControl.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Ch time Control (sec) since node start", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 22118048, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeControl.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Ch time Control (sec) since node start", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 22118160, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeControl.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Ch time Control (sec) since node start", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 22118155, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeControl.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Ch time Control (sec) since node start", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 22116857, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeControl.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Ch time Control (sec) since node start", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 22117064, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeControl.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Ch time Control (sec) since node start", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 22118009, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeFailed.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Ch time Failed (sec) since node start", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 9673, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeFailed.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Ch time Failed (sec) since node start", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6466, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeFailed.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Ch time Failed (sec) since node start", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 12880, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeFailed.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Ch time Failed (sec) since node start", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6748, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeFailed.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Ch time Failed (sec) since node start", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7154, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeFailed.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Ch time Failed (sec) since node start", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 34485, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeFailed.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Ch time Failed (sec) since node start", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 10521, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeFailed.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Ch time Failed (sec) since node start", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6498, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeIdle.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Ch time Idle (sec) since node start", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 59898322, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeIdle.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Ch time Idle (sec) since node start", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 66350777, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeIdle.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Ch time Idle (sec) since node start", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 66344340, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeIdle.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Ch time Idle (sec) since node start", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 64341691, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeIdle.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Ch time Idle (sec) since node start", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 65769312, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeIdle.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Ch time Idle (sec) since node start", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 52649939, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeIdle.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Ch time Idle (sec) since node start", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 66347542, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteChannelTimeIdle.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Ch time Idle (sec) since node start", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 66350753, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNAlternateChannels.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb alternate ch", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNAlternateChannels.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb alternate ch", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNAlternateChannels.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb alternate ch", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNAlternateChannels.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb alternate ch", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNAlternateChannels.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb alternate ch", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNAlternateChannels.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb alternate ch", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNAlternateChannels.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb alternate ch", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNAlternateChannels.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb alternate ch", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNChannels.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb ch installed", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNChannels.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb ch installed", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNChannels.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb ch installed", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNChannels.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb ch installed", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNChannels.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb ch installed", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNChannels.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb ch installed", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNChannels.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb ch installed", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNChannels.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb ch installed", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNFailedChannels.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb Failed Ch", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNFailedChannels.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb Failed Ch", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNFailedChannels.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb Failed Ch", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNFailedChannels.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb Failed Ch", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNFailedChannels.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb Failed Ch", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNFailedChannels.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb Failed Ch", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNFailedChannels.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb Failed Ch", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNFailedChannels.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb Failed Ch", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNIdleChannels.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb Idle Ch", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNIdleChannels.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb Idle Ch", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNIdleChannels.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb Idle Ch", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNIdleChannels.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb Idle Ch", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNIdleChannels.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb Idle Ch", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNIdleChannels.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb Idle Ch", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNIdleChannels.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb Idle Ch", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNIdleChannels.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb Idle Ch", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNOnAirCalls.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb Calls on air", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNOnAirCalls.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb Calls on air", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNOnAirCalls.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb Calls on air", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNOnAirCalls.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb Calls on air", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNOnAirCalls.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb Calls on air", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNOnAirCalls.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb Calls on air", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNOnAirCalls.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb Calls on air", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNOnAirCalls.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb Calls on air", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNQueuedCalls.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb Calls queued", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNQueuedCalls.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb Calls queued", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNQueuedCalls.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb Calls queued", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNQueuedCalls.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb Calls queued", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNQueuedCalls.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb Calls queued", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNQueuedCalls.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb Calls queued", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNQueuedCalls.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb Calls queued", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNQueuedCalls.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb Calls queued", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNRingingCalls.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb Calls waiting", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNRingingCalls.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb Calls waiting", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNRingingCalls.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb Calls waiting", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNRingingCalls.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb Calls waiting", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNRingingCalls.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb Calls waiting", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNRingingCalls.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb Calls waiting", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNRingingCalls.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb Calls waiting", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNRingingCalls.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb Calls waiting", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNTrafficChannels.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb Traffic Ch", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNTrafficChannels.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb Traffic Ch", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNTrafficChannels.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb Traffic Ch", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNTrafficChannels.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb Traffic Ch", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNTrafficChannels.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb Traffic Ch", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNTrafficChannels.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb Traffic Ch", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNTrafficChannels.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb Traffic Ch", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteNTrafficChannels.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb Traffic Ch", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteQueueDepth.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb Calls Queue", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteQueueDepth.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb Calls Queue", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteQueueDepth.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb Calls Queue", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteQueueDepth.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb Calls Queue", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteQueueDepth.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb Calls Queue", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteQueueDepth.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb Calls Queue", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteQueueDepth.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb Calls Queue", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteQueueDepth.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb Calls Queue", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalCalls.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb Total Calls since start", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7719378, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalCalls.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb Total Calls since start", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 135565, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalCalls.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb Total Calls since start", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 104120, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalCalls.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb Total Calls since start", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2080665, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalCalls.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb Total Calls since start", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 547890, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalCalls.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb Total Calls since start", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 12535003, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalCalls.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb Total Calls since start", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 171609, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalCalls.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb Total Calls since start", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 62547, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalChannelCalls.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Nb Total Calls wth traffic ch", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7302630, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalChannelCalls.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Nb Total Calls wth traffic ch", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 89, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalChannelCalls.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Nb Total Calls wth traffic ch", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 103, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalChannelCalls.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Nb Total Calls wth traffic ch", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1847027, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalChannelCalls.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Nb Total Calls wth traffic ch", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 302036, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalChannelCalls.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Nb Total Calls wth traffic ch", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 12465793, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalChannelCalls.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Nb Total Calls wth traffic ch", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 97, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalChannelCalls.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Nb Total Calls wth traffic ch", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 89, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalQueueTime.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Site1 Queue Secs since node start", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 427544, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalQueueTime.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Site2 Queue Secs since node start", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalQueueTime.3", "sensor_type": "tait-tnadmin", "sensor_descr": "Site3 Queue Secs since node start", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalQueueTime.4", "sensor_type": "tait-tnadmin", "sensor_descr": "Site4 Queue Secs since node start", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6111, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalQueueTime.5", "sensor_type": "tait-tnadmin", "sensor_descr": "Site5 Queue Secs since node start", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 579, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalQueueTime.6", "sensor_type": "tait-tnadmin", "sensor_descr": "Site6 Queue Secs since node start", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 8624, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalQueueTime.7", "sensor_type": "tait-tnadmin", "sensor_descr": "Site7 Queue Secs since node start", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteTotalQueueTime.8", "sensor_type": "tait-tnadmin", "sensor_descr": "Site8 Queue Secs since node start", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.13.0", "sensor_index": "tn9300CallsSwitching.0", "sensor_type": "tait-tnadmin", "sensor_descr": "Nb Calls Switching", "group": "Tn9300LocalNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.14.0", "sensor_index": "tn9300ConnectionsSwitching.0", "sensor_type": "tait-tnadmin", "sensor_descr": "Nb Connections Switching", "group": "Tn9300LocalNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.3.0", "sensor_index": "tn9300Priority.0", "sensor_type": "tait-tnadmin", "sensor_descr": "Priority of the local node", "group": "Tn9300LocalNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "tn9300RemoteNodeCallSw.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Node-1 Nb Calls switched on remote node", "group": "Tn9300RemoteNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "tn9300RemoteNodeCallSw.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Node-2 Nb Calls switched on remote node", "group": "Tn9300RemoteNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "tn9300RemoteNodeConnectSw.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Node-1 Nb Connections switched on remote node", "group": "Tn9300RemoteNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "tn9300RemoteNodeConnectSw.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Node-2 Nb Connections switched on remote node", "group": "Tn9300RemoteNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "tn9300RemotePrio.1", "sensor_type": "tait-tnadmin", "sensor_descr": "Node-1 Remote Node Prio", "group": "Tn9300RemoteNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "tn9300RemotePrio.2", "sensor_type": "tait-tnadmin", "sensor_descr": "Node-2 Remote Node Prio", "group": "Tn9300RemoteNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgwLinkState.1", "sensor_type": "tn9300DipLineNgwLinkState", "sensor_descr": "DIPINT-Site1 Dip Line to NetGW State", "group": "TN9300 DipConnections DIPINT-Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineNgwLinkState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******0", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgwLinkState.10", "sensor_type": "tn9300DipLineNgwLinkState", "sensor_descr": "DIPINT-All_Sites Dip Line to NetGW State", "group": "TN9300 DipConnections DIPINT-All_Sites", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineNgwLinkState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgwLinkState.2", "sensor_type": "tn9300DipLineNgwLinkState", "sensor_descr": "DIPINT-Site2 Dip Line to NetGW State", "group": "TN9300 DipConnections DIPINT-Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineNgwLinkState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgwLinkState.3", "sensor_type": "tn9300DipLineNgwLinkState", "sensor_descr": "DIPINT-Site3 Dip Line to NetGW State", "group": "TN9300 DipConnections DIPINT-Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineNgwLinkState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgwLinkState.4", "sensor_type": "tn9300DipLineNgwLinkState", "sensor_descr": "DIPINT-Site4 Dip Line to NetGW State", "group": "TN9300 DipConnections DIPINT-Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineNgwLinkState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgwLinkState.5", "sensor_type": "tn9300DipLineNgwLinkState", "sensor_descr": "DIPINT-Site5 Dip Line to NetGW State", "group": "TN9300 DipConnections DIPINT-Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineNgwLinkState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgwLinkState.6", "sensor_type": "tn9300DipLineNgwLinkState", "sensor_descr": "DIPINT-Site6 Dip Line to NetGW State", "group": "TN9300 DipConnections DIPINT-Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineNgwLinkState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgwLinkState.7", "sensor_type": "tn9300DipLineNgwLinkState", "sensor_descr": "DIPINT-Site7 Dip Line to NetGW State", "group": "TN9300 DipConnections DIPINT-Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineNgwLinkState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineNgwLinkState.8", "sensor_type": "tn9300DipLineNgwLinkState", "sensor_descr": "DIPINT-Site8 Dip Line to NetGW State", "group": "TN9300 DipConnections DIPINT-Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineNgwLinkState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineState.1", "sensor_type": "tn9300DipLineState", "sensor_descr": "DIPINT-Site1 Dip Line State", "group": "TN9300 DipConnections DIPINT-Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******0", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineState.10", "sensor_type": "tn9300DipLineState", "sensor_descr": "DIPINT-All_Sites Dip Line State", "group": "TN9300 DipConnections DIPINT-All_Sites", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineState.2", "sensor_type": "tn9300DipLineState", "sensor_descr": "DIPINT-Site2 Dip Line State", "group": "TN9300 DipConnections DIPINT-Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineState.3", "sensor_type": "tn9300DipLineState", "sensor_descr": "DIPINT-Site3 Dip Line State", "group": "TN9300 DipConnections DIPINT-Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineState.4", "sensor_type": "tn9300DipLineState", "sensor_descr": "DIPINT-Site4 Dip Line State", "group": "TN9300 DipConnections DIPINT-Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineState.5", "sensor_type": "tn9300DipLineState", "sensor_descr": "DIPINT-Site5 Dip Line State", "group": "TN9300 DipConnections DIPINT-Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineState.6", "sensor_type": "tn9300DipLineState", "sensor_descr": "DIPINT-Site6 Dip Line State", "group": "TN9300 DipConnections DIPINT-Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineState.7", "sensor_type": "tn9300DipLineState", "sensor_descr": "DIPINT-Site7 Dip Line State", "group": "TN9300 DipConnections DIPINT-Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300DipLineState.8", "sensor_type": "tn9300DipLineState", "sensor_descr": "DIPINT-Site8 Dip Line State", "group": "TN9300 DipConnections DIPINT-Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DipLineState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.17.0", "sensor_index": "tn9300DiskSpaceOk.0", "sensor_type": "tn9300DiskSpaceOk", "sensor_descr": "Disk Space OK", "group": "Alarms Local Node", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300DiskSpaceOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.18.0", "sensor_index": "tn9300LicenseValidity.0", "sensor_type": "tn9300LicenseValidity", "sensor_descr": "Local Node Licence", "group": "Alarms Local Node", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300LicenseValidity"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "tnRemoteNodeState.1", "sensor_type": "tn9300RemoteNodeState", "sensor_descr": "Node-1 Remote Node State", "group": "Tn9300RemoteNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300RemoteNodeState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "tnRemoteNodeState.2", "sensor_type": "tn9300RemoteNodeState", "sensor_descr": "Node-2 Remote Node State", "group": "Tn9300RemoteNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300RemoteNodeState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "tnRemoteNodeSync.1", "sensor_type": "tn9300RemoteNodeSynced", "sensor_descr": "Node-1 Remote Node Sync State", "group": "Tn9300RemoteNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300RemoteNodeSynced"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "tnRemoteNodeSync.2", "sensor_type": "tn9300RemoteNodeSynced", "sensor_descr": "Node-2 Remote Node Sync State", "group": "Tn9300RemoteNode", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300RemoteNodeSynced"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteControlChCountOk.1", "sensor_type": "tn9300SiteControlChCountOk", "sensor_descr": "Site1 Ctrl Nb Ch Match Required", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteControlChCountOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteControlChCountOk.2", "sensor_type": "tn9300SiteControlChCountOk", "sensor_descr": "Site2 Ctrl Nb Ch Match Required", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteControlChCountOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteControlChCountOk.3", "sensor_type": "tn9300SiteControlChCountOk", "sensor_descr": "Site3 Ctrl Nb Ch Match Required", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteControlChCountOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteControlChCountOk.4", "sensor_type": "tn9300SiteControlChCountOk", "sensor_descr": "Site4 Ctrl Nb Ch Match Required", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteControlChCountOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteControlChCountOk.5", "sensor_type": "tn9300SiteControlChCountOk", "sensor_descr": "Site5 Ctrl Nb Ch Match Required", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteControlChCountOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteControlChCountOk.6", "sensor_type": "tn9300SiteControlChCountOk", "sensor_descr": "Site6 Ctrl Nb Ch Match Required", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteControlChCountOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteControlChCountOk.7", "sensor_type": "tn9300SiteControlChCountOk", "sensor_descr": "Site7 Ctrl Nb Ch Match Required", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteControlChCountOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.********", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteControlChCountOk.8", "sensor_type": "tn9300SiteControlChCountOk", "sensor_descr": "Site8 Ctrl Nb Ch Match Required", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteControlChCountOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteEnabled.1", "sensor_type": "tn9300SiteEnabled", "sensor_descr": "Site1 Site Enabled State", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteEnabled"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteEnabled.2", "sensor_type": "tn9300SiteEnabled", "sensor_descr": "Site2 Site Enabled State", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteEnabled"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteEnabled.3", "sensor_type": "tn9300SiteEnabled", "sensor_descr": "Site3 Site Enabled State", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteEnabled"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteEnabled.4", "sensor_type": "tn9300SiteEnabled", "sensor_descr": "Site4 Site Enabled State", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteEnabled"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteEnabled.5", "sensor_type": "tn9300SiteEnabled", "sensor_descr": "Site5 Site Enabled State", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteEnabled"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteEnabled.6", "sensor_type": "tn9300SiteEnabled", "sensor_descr": "Site6 Site Enabled State", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteEnabled"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteEnabled.7", "sensor_type": "tn9300SiteEnabled", "sensor_descr": "Site7 Site Enabled State", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteEnabled"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteEnabled.8", "sensor_type": "tn9300SiteEnabled", "sensor_descr": "Site8 Site Enabled State", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteEnabled"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteOk.1", "sensor_type": "tn9300SiteOk", "sensor_descr": "Site1 Site State", "group": "Site Site1", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteOk.2", "sensor_type": "tn9300SiteOk", "sensor_descr": "Site2 Site State", "group": "Site Site2", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteOk.3", "sensor_type": "tn9300SiteOk", "sensor_descr": "Site3 Site State", "group": "Site Site3", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteOk.4", "sensor_type": "tn9300SiteOk", "sensor_descr": "Site4 Site State", "group": "Site Site4", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteOk.5", "sensor_type": "tn9300SiteOk", "sensor_descr": "Site5 Site State", "group": "Site Site5", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteOk.6", "sensor_type": "tn9300SiteOk", "sensor_descr": "Site6 Site State", "group": "Site Site6", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteOk.7", "sensor_type": "tn9300SiteOk", "sensor_descr": "Site7 Site State", "group": "Site Site7", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.*******.*******", "sensor_index": "TAIT-TN9300-MIB::tn9300SiteOk.8", "sensor_type": "tn9300SiteOk", "sensor_descr": "Site8 Site State", "group": "Site Site8", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tn9300SiteOk"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.********.*******3443", "sensor_index": "tnServiceState.13443", "sensor_type": "tnAdminTaitServiceState", "sensor_descr": "TN9300 DMR Trunked 03.39.02.2108191413-REL", "group": "TnAdmin", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tnAdminTaitServiceState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.********.*******443", "sensor_index": "tnServiceState.1443", "sensor_type": "tnAdminTaitServiceState", "sensor_descr": "TaitNet Administration 01.21.01.2106141514-REL", "group": "TnAdmin", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tnAdminTaitServiceState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.********.*******8443", "sensor_index": "tnServiceState.18443", "sensor_type": "tnAdminTaitServiceState", "sensor_descr": "TN9300 Channel Group Manager 01.11.00.491272-REL", "group": "TnAdmin", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tnAdminTaitServiceState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3570.********.*******0443", "sensor_index": "tnServiceState.30443", "sensor_type": "tnAdminTaitServiceState", "sensor_descr": "DMR Trunked Data API Connector 00.07.01.2105061218-REL", "group": "TnAdmin", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "tnAdminTaitServiceState"}], "state_indexes": [{"state_name": "tn9300DipLineNgwLinkState", "state_descr": "Unknown", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 3}, {"state_name": "tn9300DipLineNgwLinkState", "state_descr": "OK", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "tn9300DipLineNgwLinkState", "state_descr": "Failed", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 1}, {"state_name": "tn9300DipLineState", "state_descr": "Unknown", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 3}, {"state_name": "tn9300DipLineState", "state_descr": "Unconfigured", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "tn9300DipLineState", "state_descr": "Idle", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "tn9300DipLineState", "state_descr": "Active", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "tn9300DipLineState", "state_descr": "Failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "tn9300DiskSpaceOk", "state_descr": "OK", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "tn9300DiskSpaceOk", "state_descr": "NOK", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 1}, {"state_name": "tn9300LicenseValidity", "state_descr": "<PERSON><PERSON>", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "tn9300LicenseValidity", "state_descr": "File Not Found", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 1}, {"state_name": "tn9300LicenseValidity", "state_descr": "Invalid host ID", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 1}, {"state_name": "tn9300LicenseValidity", "state_descr": "Invalid product code", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "tn9300LicenseValidity", "state_descr": "Invalid version", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "tn9300LicenseValidity", "state_descr": "Invalid expiry date", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 1}, {"state_name": "tn9300LicenseValidity", "state_descr": "Expired", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "tn9300LicenseValidity", "state_descr": "Corrupt signature", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 1}, {"state_name": "tn9300LicenseValidity", "state_descr": "Conflicting features", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 1}, {"state_name": "tn9300LicenseValidity", "state_descr": "Invalid tier mode", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "tn9300LicenseValidity", "state_descr": "Invalid license format", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 1}, {"state_name": "tn9300RemoteNodeState", "state_descr": "Unknown", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 3}, {"state_name": "tn9300RemoteNodeState", "state_descr": "Offline", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "tn9300RemoteNodeState", "state_descr": "Program", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "tn9300RemoteNodeState", "state_descr": "Switching", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "tn9300RemoteNodeState", "state_descr": "Control", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 0}, {"state_name": "tn9300RemoteNodeState", "state_descr": "Failed", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 1}, {"state_name": "tn9300RemoteNodeState", "state_descr": "Graceful Shutdown", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 3}, {"state_name": "tn9300RemoteNodeSynced", "state_descr": "OK", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "tn9300RemoteNodeSynced", "state_descr": "Failed", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 1}, {"state_name": "tn9300RemoteNodeSynced", "state_descr": "Unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "tn9300RemoteNodeSynced", "state_descr": "None", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 3}, {"state_name": "tn9300SiteControlChCountOk", "state_descr": "Not OK", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 1}, {"state_name": "tn9300SiteControlChCountOk", "state_descr": "OK", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "tn9300SiteEnabled", "state_descr": "Disabled", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 1}, {"state_name": "tn9300SiteEnabled", "state_descr": "Enabled", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "tn9300SiteOk", "state_descr": "Not OK", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 1}, {"state_name": "tn9300SiteOk", "state_descr": "OK", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "tnAdminTaitServiceState", "state_descr": "Running", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 0}, {"state_name": "tnAdminTaitServiceState", "state_descr": "Watchdog Stopped", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 1}, {"state_name": "tnAdminTaitServiceState", "state_descr": "Application Stopped", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "tnAdminTaitServiceState", "state_descr": "Stopped", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "tnAdminTaitServiceState", "state_descr": "Not Installed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 3}]}, "poller": "matches discovery"}, "storage": {"discovery": {"storage": [{"type": "hrstorage", "storage_index": "31", "storage_type": "hrStorageFixedDisk", "storage_descr": "/", "storage_size": 478059720704, "storage_size_oid": null, "storage_units": 4096, "storage_used": 21069492224, "storage_used_oid": ".*******.********.3.1.6.31", "storage_free": 456990228480, "storage_free_oid": null, "storage_perc": 4, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "37", "storage_type": "hrStorageFixedDisk", "storage_descr": "/dev/shm", "storage_size": 8112455680, "storage_size_oid": null, "storage_units": 4096, "storage_used": 0, "storage_used_oid": ".*******.********.3.1.6.37", "storage_free": 8112455680, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "39", "storage_type": "hrStorageFixedDisk", "storage_descr": "/run", "storage_size": 8112455680, "storage_size_oid": null, "storage_units": 4096, "storage_used": 873046016, "storage_used_oid": ".*******.********.3.1.6.39", "storage_free": 7239409664, "storage_free_oid": null, "storage_perc": 11, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "60", "storage_type": "hrStorageFixedDisk", "storage_descr": "/ramdisk", "storage_size": 268435456, "storage_size_oid": null, "storage_units": 4096, "storage_used": 36864, "storage_used_oid": ".*******.********.3.1.6.60", "storage_free": 268398592, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "61", "storage_type": "hrStorageFixedDisk", "storage_descr": "/boot", "storage_size": 517713920, "storage_size_oid": null, "storage_units": 4096, "storage_used": 179527680, "storage_used_oid": ".*******.********.3.1.6.61", "storage_free": 338186240, "storage_free_oid": null, "storage_perc": 35, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hrstorage", "storage_index": "62", "storage_type": "hrStorageFixedDisk", "storage_descr": "/boot/efi", "storage_size": 209489920, "storage_size_oid": null, "storage_units": 4096, "storage_used": 11726848, "storage_used_oid": ".*******.********.3.1.6.62", "storage_free": 197763072, "storage_free_oid": null, "storage_perc": 6, "storage_perc_oid": null, "storage_perc_warn": 60}]}, "poller": "matches discovery"}}