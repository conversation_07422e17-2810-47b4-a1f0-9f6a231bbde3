{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.8000", "sysDescr": "UHP VSAT Terminal Software", "sysContact": "<private>", "version": "50724909", "hardware": null, "features": null, "location": "<private>", "os": "uhp", "type": "wireless", "serial": "1074225446", "icon": "uhp.png"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Ethernet", "ifName": "Ethernet", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Ethernet", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Demodulator-2", "ifName": "Demodulator-2", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "frameRelay", "ifAlias": "Demodulator-2", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Demodulator-1", "ifName": "Demodulator-1", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "frameRelay", "ifAlias": "Demodulator-1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Modulator", "ifName": "Modulator", "portName": null, "ifIndex": 4, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "frameRelay", "ifAlias": "Modulator", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Network", "ifName": "Network", "portName": null, "ifIndex": 5, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "frameRelay", "ifAlias": "Network", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Ethernet", "ifName": "Ethernet", "portName": null, "ifIndex": 1, "ifSpeed": 10000000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 2000, "ifType": "ethernetCsmacd", "ifAlias": "Ethernet", "ifPhysAddress": "000000000000", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 37293586, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 10164032, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 37293284, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 10163856, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 39, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Demodulator-2", "ifName": "Demodulator-2", "portName": null, "ifIndex": 2, "ifSpeed": 1000, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 2004, "ifType": "frameRelay", "ifAlias": "Demodulator-2", "ifPhysAddress": "000000000000", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 782, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Demodulator-1", "ifName": "Demodulator-1", "portName": null, "ifIndex": 3, "ifSpeed": 6666, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 2004, "ifType": "frameRelay", "ifAlias": "Demodulator-1", "ifPhysAddress": "000000000000", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 1572110352, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 949, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 1570794184, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 1, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Modulator", "ifName": "Modulator", "portName": null, "ifIndex": 4, "ifSpeed": 900, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 2004, "ifType": "frameRelay", "ifAlias": "Modulator", "ifPhysAddress": "000000000000", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 38824925, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 38824004, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 1, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Network", "ifName": "Network", "portName": null, "ifIndex": 5, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 2004, "ifType": "frameRelay", "ifAlias": "Network", "ifPhysAddress": "000000000000", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 0, "processor_oid": ".*******.4.1.8000.********", "processor_index": "0", "processor_type": "uhp", "processor_usage": 14, "processor_descr": "Processor", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.22.2.40", "sensor_index": "inBytes1.0", "sensor_type": "uhp", "sensor_descr": "Traffic", "group": "Demodulator 1", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 506116109.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.22.3.40", "sensor_index": "inBytes2.0", "sensor_type": "uhp", "sensor_descr": "Traffic", "group": "Demodulator 2", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesA.0", "sensor_type": "uhp", "sensor_descr": "Traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 4832078.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesC.0", "sensor_type": "uhp", "sensor_descr": "Control traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 16.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesP1.0", "sensor_type": "uhp", "sensor_descr": "P1(low) priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 4832504.125, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesP2.0", "sensor_type": "uhp", "sensor_descr": "P2 priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesP3.0", "sensor_type": "uhp", "sensor_descr": "P3 priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesP4.0", "sensor_type": "uhp", "sensor_descr": "P4(med) priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesP5.0", "sensor_type": "uhp", "sensor_descr": "P5 priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.*********.0", "sensor_index": "outBytesP6.0", "sensor_type": "uhp", "sensor_descr": "P6 priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesP7.0", "sensor_type": "uhp", "sensor_descr": "P7(high) priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBA.0", "sensor_type": "uhp", "sensor_descr": "Queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBP1.0", "sensor_type": "uhp", "sensor_descr": "P1(low) queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 147.625, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBP2.0", "sensor_type": "uhp", "sensor_descr": "P2 queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBP3.0", "sensor_type": "uhp", "sensor_descr": "P3 queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBP4.0", "sensor_type": "uhp", "sensor_descr": "P4(med) queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBP5.0", "sensor_type": "uhp", "sensor_descr": "P5 queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.*********.0", "sensor_index": "queueLenBP6.0", "sensor_type": "uhp", "sensor_descr": "P6 queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBP7.0", "sensor_type": "uhp", "sensor_descr": "P7(high) queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "sectionBW.0", "sensor_type": "uhp", "sensor_descr": "Bandwidth of single slot in frame", "group": null, "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 1403.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsA.0", "sensor_type": "uhp", "sensor_descr": "Dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsP1.0", "sensor_type": "uhp", "sensor_descr": "P1(low) dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsP2.0", "sensor_type": "uhp", "sensor_descr": "P2 dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsP3.0", "sensor_type": "uhp", "sensor_descr": "P3 dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsP4.0", "sensor_type": "uhp", "sensor_descr": "P4(med) dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsP5.0", "sensor_type": "uhp", "sensor_descr": "P5 dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.*********.0", "sensor_index": "dropsP6.0", "sensor_type": "uhp", "sensor_descr": "P6 dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsP7.0", "sensor_type": "uhp", "sensor_descr": "P7(high) dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "fpLost.0", "sensor_type": "uhp", "sensor_descr": "Lost frame plans", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "frameDelay.0", "sensor_type": "uhp", "sensor_descr": "TX-RX processing delay", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "frameDuration.0", "sensor_type": "uhp", "sensor_descr": "Frame duration", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "freeSlots.0", "sensor_type": "uhp", "sensor_descr": "Free-allocated slots in current frame", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "hardErrors.0", "sensor_type": "uhp", "sensor_descr": "Hard errors of TTS/Tdelta", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "hubTTSconfidence.0", "sensor_type": "uhp", "sensor_descr": "HUB TTS confidence 0-64", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 64, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "inSvlanBytes.0", "sensor_type": "uhp", "sensor_descr": "Packets received via SVLAN 0", "group": "VLAN 0", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "inVlanBytes.0", "sensor_type": "uhp", "sensor_descr": "Bytes received via VLAN 0", "group": "VLAN 0", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1612, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "lvlOffset.0", "sensor_type": "uhp", "sensor_descr": "Reference level C/N level offset", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "netRequest.0", "sensor_type": "uhp", "sensor_descr": "Requests of all stations", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "numStations.0", "sensor_type": "uhp", "sensor_descr": "Stations per ACM channel", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsA.0", "sensor_type": "uhp", "sensor_descr": "Traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 354999, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsC.0", "sensor_type": "uhp", "sensor_descr": "Control traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsP1.0", "sensor_type": "uhp", "sensor_descr": "P1(low) priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 355055, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsP2.0", "sensor_type": "uhp", "sensor_descr": "P2 priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsP3.0", "sensor_type": "uhp", "sensor_descr": "P3 priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsP4.0", "sensor_type": "uhp", "sensor_descr": "P4(med) priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsP5.0", "sensor_type": "uhp", "sensor_descr": "P5 priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.*********.0", "sensor_index": "outPktsP6.0", "sensor_type": "uhp", "sensor_descr": "P6 priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsP7.0", "sensor_type": "uhp", "sensor_descr": "P7(high) priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "outSvlanBytes.0", "sensor_type": "uhp", "sensor_descr": "Bytes tranmitted via SVLAN 0", "group": "VLAN 0", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "outVlanBytes.0", "sensor_type": "uhp", "sensor_descr": "Bytes transmitted via VLAN 0", "group": "VLAN 0", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPA.0", "sensor_type": "uhp", "sensor_descr": "Queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPP1.0", "sensor_type": "uhp", "sensor_descr": "P1(low) queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPP2.0", "sensor_type": "uhp", "sensor_descr": "P2 queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPP3.0", "sensor_type": "uhp", "sensor_descr": "P3 queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPP4.0", "sensor_type": "uhp", "sensor_descr": "P4(med) queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPP5.0", "sensor_type": "uhp", "sensor_descr": "P5 queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.*********.0", "sensor_index": "queueLenPP6.0", "sensor_type": "uhp", "sensor_descr": "P6 queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPP7.0", "sensor_type": "uhp", "sensor_descr": "P7(high) queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "remnrtRequest.0", "sensor_type": "uhp", "sensor_descr": "Request for non-real-time bandwidth", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "remrtRequest.0", "sensor_type": "uhp", "sensor_descr": "Request for real-time bandwidth", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "softErrors.0", "sensor_type": "uhp", "sensor_descr": "Soft errors of TTS/Tdelta", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "tdtConfidence.0", "sensor_type": "uhp", "sensor_descr": "Tdelta confidence 0-64", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 44, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********0.0", "sensor_index": "timeAdjust.0", "sensor_type": "uhp", "sensor_descr": "TX timing adjustment", "group": null, "sensor_divisor": 100000000, "sensor_multiplier": 1, "sensor_current": -0.00021457, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "timeOffset.0", "sensor_type": "uhp", "sensor_descr": "Timing offset", "group": null, "sensor_divisor": 100000000, "sensor_multiplier": 1, "sensor_current": -0.00021457, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "unroutedPkts.0", "sensor_type": "uhp", "sensor_descr": "Unroutable packets", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "inLvl1.0", "sensor_type": "uhp", "sensor_descr": "Input level", "group": "Demodulator 1", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 11.9, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "inLvl2.0", "sensor_type": "uhp", "sensor_descr": "Input level", "group": "Demodulator 2", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 8, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "inputLevel.0", "sensor_type": "uhp", "sensor_descr": "Input baseband RF", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 11.2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0.0", "sensor_index": "inputLevel.0.0", "sensor_type": "uhp", "sensor_descr": "Input baseband RF", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 11.4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "lvlAdjust.0", "sensor_type": "uhp", "sensor_descr": "TX power level adjustment", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 429496727.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "txLevel.0", "sensor_type": "uhp", "sensor_descr": "TX level", "group": "Modulator", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 25.9, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "txLevelDelta.0", "sensor_type": "uhp", "sensor_descr": "TX level delta", "group": "Modulator", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -2.1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "txMaxLevel.0", "sensor_type": "uhp", "sensor_descr": "Max TLC level", "group": "Modulator", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "frqAdjust.0", "sensor_type": "uhp", "sensor_descr": "TX frequency adjustment", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 0, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "frqOffset.0", "sensor_type": "uhp", "sensor_descr": "Frequency offset", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 0, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "hubTTS.0", "sensor_type": "uhp", "sensor_descr": "HUB TTS", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 127391, "sensor_limit": 133760.55, "sensor_limit_warn": null, "sensor_limit_low": 121021.45, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "lbandOffset1.0", "sensor_type": "uhp", "sensor_descr": "PLL and carrier offset", "group": "Demodulator 1", "sensor_divisor": 1, "sensor_multiplier": 1000, "sensor_current": 2000, "sensor_limit": 2100, "sensor_limit_warn": null, "sensor_limit_low": 1900, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "lbandOffset2.0", "sensor_type": "uhp", "sensor_descr": "PLL and carrier offset", "group": "Demodulator 2", "sensor_divisor": 1, "sensor_multiplier": 1000, "sensor_current": -50687000, "sensor_limit": -48152650, "sensor_limit_warn": null, "sensor_limit_low": -53221350, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "remoteTTS.0", "sensor_type": "uhp", "sensor_descr": "Remote TTS", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4294965151, "sensor_limit": 4509713408.55, "sensor_limit_warn": null, "sensor_limit_low": 4080216893.45, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "tdelta.0", "sensor_type": "uhp", "sensor_descr": "Delta between station and hub", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": -482073969, "sensor_limit": -457970270.55, "sensor_limit_warn": null, "sensor_limit_low": -506177667.45, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "percent", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "buffers.0", "sensor_type": "uhp", "sensor_descr": "Buffer usage", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "percent", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "netLoad.0", "sensor_type": "uhp", "sensor_descr": "Network load", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "percent", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "searchState.0", "sensor_type": "uhp", "sensor_descr": "Search state", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "snr", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "lbandSNR1.0", "sensor_type": "uhp", "sensor_descr": "<PERSON><PERSON> at L-band", "group": "Demodulator 1", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 13.5, "sensor_limit": 14.175, "sensor_limit_warn": null, "sensor_limit_low": 12.825, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "snr", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "lbandSNR2.0", "sensor_type": "uhp", "sensor_descr": "<PERSON><PERSON> at L-band", "group": "Demodulator 2", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 0, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "redundancy.0", "sensor_type": "Current redundancy state", "sensor_descr": "Redundancy status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "Current redundancy state"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "rxState.0", "sensor_type": "Current redundancy state", "sensor_descr": "RX status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 136, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "Current redundancy state"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "txControl.0", "sensor_type": "Current TX status", "sensor_descr": "TX status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "Current TX status"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "netState.0", "sensor_type": "Operation sequence level", "sensor_descr": "Operation status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 17, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "Operation sequence level"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "stationState.0", "sensor_type": "Remote initialization sequence level", "sensor_descr": "Remote status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 17, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "Remote initialization sequence level"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "serverStatus.0", "sensor_type": "Server reply status", "sensor_descr": "Server reply status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 17, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "Server reply status"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "temperature.0", "sensor_type": "uhp", "sensor_descr": "Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 18.33, "sensor_limit": 38.33, "sensor_limit_warn": null, "sensor_limit_low": 8.33, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": "fahrenheit_to_celsius", "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "Current redundancy state", "state_descr": "no RX lock", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "Current redundancy state", "state_descr": "absence of coordinates", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "Current redundancy state", "state_descr": "absence of TX status", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "Current TX status", "state_descr": "off", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "Current TX status", "state_descr": "on", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "off", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "init", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "noConfig", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "useConfig", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "redundant", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "startRX", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "cotmPointing", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "startHubTX", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "noRX", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "identify", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "getNetConfig", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "calcD<PERSON>ys", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "startTDMA", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "startTX", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "acquisition", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "adjustment", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "noStations", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "operation", "state_draw_graph": 0, "state_value": 17, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "off", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "init", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "noConfig", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "useConfig", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "redundant", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "startRX", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "cotmPointing", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "startHubTX", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "noRX", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "identify", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "getNetConfig", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "calcD<PERSON>ys", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "startTDMA", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "startTX", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "acquisition", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "adjustment", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "noStations", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "operation", "state_draw_graph": 0, "state_value": 17, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "off", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "init", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "noConfig", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "useConfig", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "redundant", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "startRX", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "cotmPointing", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "startHubTX", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "noRX", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "identify", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "getNetConfig", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "calcD<PERSON>ys", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "startTDMA", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "startTX", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "acquisition", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "adjustment", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "noStations", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "operation", "state_draw_graph": 0, "state_value": 17, "state_generic_value": 0}]}, "poller": {"sensors": [{"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.22.2.40", "sensor_index": "inBytes1.0", "sensor_type": "uhp", "sensor_descr": "Traffic", "group": "Demodulator 1", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 506116109.5, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.22.3.40", "sensor_index": "inBytes2.0", "sensor_type": "uhp", "sensor_descr": "Traffic", "group": "Demodulator 2", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesA.0", "sensor_type": "uhp", "sensor_descr": "Traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 4832078.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesC.0", "sensor_type": "uhp", "sensor_descr": "Control traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 16.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesP1.0", "sensor_type": "uhp", "sensor_descr": "P1(low) priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 4832504.125, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesP2.0", "sensor_type": "uhp", "sensor_descr": "P2 priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesP3.0", "sensor_type": "uhp", "sensor_descr": "P3 priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesP4.0", "sensor_type": "uhp", "sensor_descr": "P4(med) priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesP5.0", "sensor_type": "uhp", "sensor_descr": "P5 priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.*********.0", "sensor_index": "outBytesP6.0", "sensor_type": "uhp", "sensor_descr": "P6 priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outBytesP7.0", "sensor_type": "uhp", "sensor_descr": "P7(high) priority traffic", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBA.0", "sensor_type": "uhp", "sensor_descr": "Queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBP1.0", "sensor_type": "uhp", "sensor_descr": "P1(low) queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 147.625, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBP2.0", "sensor_type": "uhp", "sensor_descr": "P2 queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBP3.0", "sensor_type": "uhp", "sensor_descr": "P3 queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBP4.0", "sensor_type": "uhp", "sensor_descr": "P4(med) queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBP5.0", "sensor_type": "uhp", "sensor_descr": "P5 queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.*********.0", "sensor_index": "queueLenBP6.0", "sensor_type": "uhp", "sensor_descr": "P6 queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenBP7.0", "sensor_type": "uhp", "sensor_descr": "P7(high) queue length", "group": "Modulator", "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "bitrate", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "sectionBW.0", "sensor_type": "uhp", "sensor_descr": "Bandwidth of single slot in frame", "group": null, "sensor_divisor": 8, "sensor_multiplier": 1, "sensor_current": 1403.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsA.0", "sensor_type": "uhp", "sensor_descr": "Dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsP1.0", "sensor_type": "uhp", "sensor_descr": "P1(low) dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsP2.0", "sensor_type": "uhp", "sensor_descr": "P2 dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsP3.0", "sensor_type": "uhp", "sensor_descr": "P3 dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsP4.0", "sensor_type": "uhp", "sensor_descr": "P4(med) dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsP5.0", "sensor_type": "uhp", "sensor_descr": "P5 dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.*********.0", "sensor_index": "dropsP6.0", "sensor_type": "uhp", "sensor_descr": "P6 dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "dropsP7.0", "sensor_type": "uhp", "sensor_descr": "P7(high) dropped packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "fpLost.0", "sensor_type": "uhp", "sensor_descr": "Lost frame plans", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "frameDelay.0", "sensor_type": "uhp", "sensor_descr": "TX-RX processing delay", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "frameDuration.0", "sensor_type": "uhp", "sensor_descr": "Frame duration", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "freeSlots.0", "sensor_type": "uhp", "sensor_descr": "Free-allocated slots in current frame", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "hardErrors.0", "sensor_type": "uhp", "sensor_descr": "Hard errors of TTS/Tdelta", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "hubTTSconfidence.0", "sensor_type": "uhp", "sensor_descr": "HUB TTS confidence 0-64", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 64, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "inSvlanBytes.0", "sensor_type": "uhp", "sensor_descr": "Packets received via SVLAN 0", "group": "VLAN 0", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "inVlanBytes.0", "sensor_type": "uhp", "sensor_descr": "Bytes received via VLAN 0", "group": "VLAN 0", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1612, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "lvlOffset.0", "sensor_type": "uhp", "sensor_descr": "Reference level C/N level offset", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "netRequest.0", "sensor_type": "uhp", "sensor_descr": "Requests of all stations", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "numStations.0", "sensor_type": "uhp", "sensor_descr": "Stations per ACM channel", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsA.0", "sensor_type": "uhp", "sensor_descr": "Traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 354999, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsC.0", "sensor_type": "uhp", "sensor_descr": "Control traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsP1.0", "sensor_type": "uhp", "sensor_descr": "P1(low) priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 355055, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsP2.0", "sensor_type": "uhp", "sensor_descr": "P2 priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsP3.0", "sensor_type": "uhp", "sensor_descr": "P3 priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsP4.0", "sensor_type": "uhp", "sensor_descr": "P4(med) priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsP5.0", "sensor_type": "uhp", "sensor_descr": "P5 priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.*********.0", "sensor_index": "outPktsP6.0", "sensor_type": "uhp", "sensor_descr": "P6 priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "outPktsP7.0", "sensor_type": "uhp", "sensor_descr": "P7(high) priority traffic in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "outSvlanBytes.0", "sensor_type": "uhp", "sensor_descr": "Bytes tranmitted via SVLAN 0", "group": "VLAN 0", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1612, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 0, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "outVlanBytes.0", "sensor_type": "uhp", "sensor_descr": "Bytes transmitted via VLAN 0", "group": "VLAN 0", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPA.0", "sensor_type": "uhp", "sensor_descr": "Queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 0, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPP1.0", "sensor_type": "uhp", "sensor_descr": "P1(low) queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPP2.0", "sensor_type": "uhp", "sensor_descr": "P2 queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPP3.0", "sensor_type": "uhp", "sensor_descr": "P3 queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPP4.0", "sensor_type": "uhp", "sensor_descr": "P4(med) queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPP5.0", "sensor_type": "uhp", "sensor_descr": "P5 queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.*********.0", "sensor_index": "queueLenPP6.0", "sensor_type": "uhp", "sensor_descr": "P6 queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "queueLenPP7.0", "sensor_type": "uhp", "sensor_descr": "P7(high) queue length in packets", "group": "Modulator", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "remnrtRequest.0", "sensor_type": "uhp", "sensor_descr": "Request for non-real-time bandwidth", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "remrtRequest.0", "sensor_type": "uhp", "sensor_descr": "Request for real-time bandwidth", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "softErrors.0", "sensor_type": "uhp", "sensor_descr": "Soft errors of TTS/Tdelta", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "tdtConfidence.0", "sensor_type": "uhp", "sensor_descr": "Tdelta confidence 0-64", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 44, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********0.0", "sensor_index": "timeAdjust.0", "sensor_type": "uhp", "sensor_descr": "TX timing adjustment", "group": null, "sensor_divisor": 100000000, "sensor_multiplier": 1, "sensor_current": -0.00021457, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "timeOffset.0", "sensor_type": "uhp", "sensor_descr": "Timing offset", "group": null, "sensor_divisor": 100000000, "sensor_multiplier": 1, "sensor_current": -0.00021457, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "unroutedPkts.0", "sensor_type": "uhp", "sensor_descr": "Unroutable packets", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "inLvl1.0", "sensor_type": "uhp", "sensor_descr": "Input level", "group": "Demodulator 1", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 11.9, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "inLvl2.0", "sensor_type": "uhp", "sensor_descr": "Input level", "group": "Demodulator 2", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 8, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "inputLevel.0", "sensor_type": "uhp", "sensor_descr": "Input baseband RF", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 11.4, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 11.2, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0.0", "sensor_index": "inputLevel.0.0", "sensor_type": "uhp", "sensor_descr": "Input baseband RF", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 11.4, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "lvlAdjust.0", "sensor_type": "uhp", "sensor_descr": "TX power level adjustment", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 429496727.5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "txLevel.0", "sensor_type": "uhp", "sensor_descr": "TX level", "group": "Modulator", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 25.9, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "txLevelDelta.0", "sensor_type": "uhp", "sensor_descr": "TX level delta", "group": "Modulator", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": -2.1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "dbm", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "txMaxLevel.0", "sensor_type": "uhp", "sensor_descr": "Max TLC level", "group": "Modulator", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "frqAdjust.0", "sensor_type": "uhp", "sensor_descr": "TX frequency adjustment", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 0, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "frqOffset.0", "sensor_type": "uhp", "sensor_descr": "Frequency offset", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 0, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "hubTTS.0", "sensor_type": "uhp", "sensor_descr": "HUB TTS", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 127391, "sensor_limit": 133760.55, "sensor_limit_warn": null, "sensor_limit_low": 121021.45, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "lbandOffset1.0", "sensor_type": "uhp", "sensor_descr": "PLL and carrier offset", "group": "Demodulator 1", "sensor_divisor": 1, "sensor_multiplier": 1000, "sensor_current": 2000, "sensor_limit": 2100, "sensor_limit_warn": null, "sensor_limit_low": 1900, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "lbandOffset2.0", "sensor_type": "uhp", "sensor_descr": "PLL and carrier offset", "group": "Demodulator 2", "sensor_divisor": 1, "sensor_multiplier": 1000, "sensor_current": -50687000, "sensor_limit": -48152650, "sensor_limit_warn": null, "sensor_limit_low": -53221350, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "remoteTTS.0", "sensor_type": "uhp", "sensor_descr": "Remote TTS", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4294965151, "sensor_limit": 4509713408.55, "sensor_limit_warn": null, "sensor_limit_low": 4080216893.45, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "tdelta.0", "sensor_type": "uhp", "sensor_descr": "Delta between station and hub", "group": "TTS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": -457970270.55, "sensor_limit_warn": null, "sensor_limit_low": -506177667.45, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": -482073969, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "percent", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "buffers.0", "sensor_type": "uhp", "sensor_descr": "Buffer usage", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "percent", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "netLoad.0", "sensor_type": "uhp", "sensor_descr": "Network load", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "percent", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "searchState.0", "sensor_type": "uhp", "sensor_descr": "Search state", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "snr", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "lbandSNR1.0", "sensor_type": "uhp", "sensor_descr": "<PERSON><PERSON> at L-band", "group": "Demodulator 1", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 13.5, "sensor_limit": 14.175, "sensor_limit_warn": null, "sensor_limit_low": 12.825, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "snr", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "lbandSNR2.0", "sensor_type": "uhp", "sensor_descr": "<PERSON><PERSON> at L-band", "group": "Demodulator 2", "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 0, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "redundancy.0", "sensor_type": "Current redundancy state", "sensor_descr": "Redundancy status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 5, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "Current redundancy state"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "rxState.0", "sensor_type": "Current redundancy state", "sensor_descr": "RX status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 136, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "Current redundancy state"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "txControl.0", "sensor_type": "Current TX status", "sensor_descr": "TX status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "Current TX status"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "netState.0", "sensor_type": "Operation sequence level", "sensor_descr": "Operation status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 17, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "Operation sequence level"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "stationState.0", "sensor_type": "Remote initialization sequence level", "sensor_descr": "Remote status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 17, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "Remote initialization sequence level"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********.0", "sensor_index": "serverStatus.0", "sensor_type": "Server reply status", "sensor_descr": "Server reply status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 17, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "Server reply status"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.8000.********", "sensor_index": "temperature.0", "sensor_type": "uhp", "sensor_descr": "Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 57.22, "sensor_limit": 38.33, "sensor_limit_warn": null, "sensor_limit_low": 8.33, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": 18.33, "user_func": "fahrenheit_to_celsius", "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "Current redundancy state", "state_descr": "no RX lock", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "Current redundancy state", "state_descr": "absence of coordinates", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "Current redundancy state", "state_descr": "absence of TX status", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "Current TX status", "state_descr": "off", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "Current TX status", "state_descr": "on", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "off", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "init", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "noConfig", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "useConfig", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "redundant", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "startRX", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "cotmPointing", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "startHubTX", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "noRX", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "identify", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "getNetConfig", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "calcD<PERSON>ys", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "startTDMA", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "startTX", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "acquisition", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "adjustment", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "noStations", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 0}, {"state_name": "Operation sequence level", "state_descr": "operation", "state_draw_graph": 0, "state_value": 17, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "off", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "init", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "noConfig", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "useConfig", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "redundant", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "startRX", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "cotmPointing", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "startHubTX", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "noRX", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "identify", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "getNetConfig", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "calcD<PERSON>ys", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "startTDMA", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "startTX", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "acquisition", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "adjustment", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "noStations", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 0}, {"state_name": "Remote initialization sequence level", "state_descr": "operation", "state_draw_graph": 0, "state_value": 17, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "off", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "init", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "noConfig", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "useConfig", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "redundant", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "startRX", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "cotmPointing", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "startHubTX", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "noRX", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "identify", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "getNetConfig", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "calcD<PERSON>ys", "state_draw_graph": 0, "state_value": 11, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "startTDMA", "state_draw_graph": 0, "state_value": 12, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "startTX", "state_draw_graph": 0, "state_value": 13, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "acquisition", "state_draw_graph": 0, "state_value": 14, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "adjustment", "state_draw_graph": 0, "state_value": 15, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "noStations", "state_draw_graph": 0, "state_value": 16, "state_generic_value": 0}, {"state_name": "Server reply status", "state_descr": "operation", "state_draw_graph": 0, "state_value": 17, "state_generic_value": 0}]}}}