{"os": {"discovery": {"devices": [{"sysName": null, "sysObjectID": ".*******.4.1.5597.7", "sysDescr": "Linux ntp-name 4.9.307 #1 SMP Wed Mar 16 11:49:02 UTC 2022 armv7l", "sysContact": "<private>", "version": null, "hardware": null, "features": null, "location": null, "os": "mbg-me<PERSON><PERSON><PERSON>", "type": "timing", "serial": null, "icon": "meinberg.svg"}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.*******", "sensor_index": "mbgOsPtpParentDsGmClockVariance.1", "sensor_type": "mbg-me<PERSON><PERSON><PERSON>", "sensor_descr": "ec:46:70:ff:fe:0c:fb:fb master variance", "group": "PTP", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 20061, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.*******", "sensor_index": "mbgOsPtpParentDsGmPrio1.1", "sensor_type": "mbg-me<PERSON><PERSON><PERSON>", "sensor_descr": "ec:46:70:ff:fe:0c:fb:fb master Priority", "group": "PTP", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 128, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.1.6.1", "sensor_index": "mbgOsReceiverGoodSv.1", "sensor_type": "mbg-me<PERSON><PERSON><PERSON>", "sensor_descr": "Current number of good/usable satellites", "group": "GNSS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 24, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": 5, "sensor_limit_low_warn": 10, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.1.7.1", "sensor_index": "mbgOsReceiverSv.1", "sensor_type": "mbg-me<PERSON><PERSON><PERSON>", "sensor_descr": "Current number of satellites visible", "group": "GNSS", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 33, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": 10, "sensor_limit_low_warn": 15, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.1.3.0", "sensor_index": "mbgOsSysrefGlbStateMasterRefAccuracy.0", "sensor_type": "mbg-me<PERSON><PERSON><PERSON>", "sensor_descr": "Master reference accuracy (ns)", "group": "PRECISION", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.1.4.0", "sensor_index": "mbgOsSysrefGlbStateMasterRefVariance.0", "sensor_type": "mbg-me<PERSON><PERSON><PERSON>", "sensor_descr": "Master reference variance", "group": "PRECISION", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 13563, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.*******", "sensor_index": "ptp_1_unicast_slave}", "sensor_type": "mbg-me<PERSON><PERSON><PERSON>", "sensor_descr": "Unicast Slave: lan2:4", "group": "PTP", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.*******", "sensor_index": "ptp_2_unicast_slave}", "sensor_type": "mbg-me<PERSON><PERSON><PERSON>", "sensor_descr": "Unicast Slave: lan0", "group": "PTP", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.*******.10", "sensor_index": "ptp_references_10}", "sensor_type": "mbg-me<PERSON><PERSON><PERSON>", "sensor_descr": "Priority 2: NTP1 (CLK1) - offset (ns)", "group": "REFERENCES", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 48000, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "count", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.*******.5", "sensor_index": "ptp_references_5}", "sensor_type": "mbg-me<PERSON><PERSON><PERSON>", "sensor_descr": "Priority 1: GNSS1 (CLK1) - offset (ns)", "group": "REFERENCES", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 9, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.*******", "sensor_index": "ptp_1}_state", "sensor_type": "mbgOsPtpMiscTableEntry", "sensor_descr": "Interface: lan2:4", "group": "PTP", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "mbgOsPtpMiscTableEntry"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.*******", "sensor_index": "ptp_2}_state", "sensor_type": "mbgOsPtpMiscTableEntry", "sensor_descr": "Interface: lan0", "group": "PTP", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "mbgOsPtpMiscTableEntry"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.*******", "sensor_index": "mbgOsPtpParentDsGmClockAccuracy.1", "sensor_type": "mbgOsPtpParentDsTableEntry", "sensor_descr": "ec:46:70:ff:fe:0c:fb:fb master accuracy", "group": "PTP", "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 33, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "mbgOsPtpParentDsTableEntry"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.1.3.1", "sensor_index": "mbgOsReceiverState.1", "sensor_type": "mbgOsReceiverState", "sensor_descr": "Receiver status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "mbgOsReceiverState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.5597.*******.1.5.0", "sensor_index": "mbgOsSysrefGlbStateSysSync.0", "sensor_type": "mbgOsSysrefGlbStateSysSync", "sensor_descr": "System global sync state", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "mbgOsSysrefGlbStateSysSync"}], "state_indexes": [{"state_name": "mbgOsPtpMiscTableEntry", "state_descr": "NOT RUN", "state_draw_graph": 1, "state_value": 0, "state_generic_value": 3}, {"state_name": "mbgOsPtpMiscTableEntry", "state_descr": "RUNNING", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "ok", "state_draw_graph": 1, "state_value": -2, "state_generic_value": 3}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<25ns", "state_draw_graph": 1, "state_value": 32, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<100ns", "state_draw_graph": 1, "state_value": 33, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<250ns", "state_draw_graph": 1, "state_value": 34, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<1μs", "state_draw_graph": 1, "state_value": 35, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<2.5μs", "state_draw_graph": 1, "state_value": 36, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<10μs", "state_draw_graph": 1, "state_value": 37, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<25μs", "state_draw_graph": 1, "state_value": 38, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<100μs", "state_draw_graph": 1, "state_value": 39, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<250μs", "state_draw_graph": 1, "state_value": 40, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<1ms", "state_draw_graph": 1, "state_value": 41, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<2-5ms", "state_draw_graph": 1, "state_value": 42, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<10ms", "state_draw_graph": 1, "state_value": 43, "state_generic_value": 0}, {"state_name": "mbgOsPtpParentDsTableEntry", "state_descr": "<25ms", "state_draw_graph": 1, "state_value": 44, "state_generic_value": 0}, {"state_name": "mbgOsReceiverState", "state_descr": "noData", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "mbgOsReceiverState", "state_descr": "waitingForData", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 1}, {"state_name": "mbgOsReceiverState", "state_descr": "antennaShortCircuit", "state_draw_graph": 2, "state_value": 2, "state_generic_value": 2}, {"state_name": "mbgOsReceiverState", "state_descr": "antennaDisconnected", "state_draw_graph": 3, "state_value": 3, "state_generic_value": 2}, {"state_name": "mbgOsReceiverState", "state_descr": "coldBoot", "state_draw_graph": 4, "state_value": 4, "state_generic_value": 1}, {"state_name": "mbgOsReceiverState", "state_descr": "warmBoot", "state_draw_graph": 5, "state_value": 5, "state_generic_value": 1}, {"state_name": "mbgOsReceiverState", "state_descr": "synchronized", "state_draw_graph": 6, "state_value": 6, "state_generic_value": 0}, {"state_name": "mbgOsSysrefGlbStateSysSync", "state_descr": "notSynchronized", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 2}, {"state_name": "mbgOsSysrefGlbStateSysSync", "state_descr": "synchronized", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}]}, "poller": "matches discovery"}, "availability": {"poller": {"availability": [{"duration": 86400, "availability_perc": "100.000000"}, {"duration": 604800, "availability_perc": "100.000000"}, {"duration": 2592000, "availability_perc": "100.000000"}, {"duration": 31536000, "availability_perc": "100.000000"}]}}}