{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.232.9.4.11", "sysDescr": "Integrated Lights-Out 5 2.71 Jul 04 2022", "sysContact": "<private>", "version": "2.71", "hardware": "ProLiant DL360 Gen10", "features": null, "location": null, "os": "hpe-ilo", "type": "appliance", "serial": "CZXXXXXXXX", "icon": "hpe.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "ens2f0", "ifName": "ens2f0", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "ens2f0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "ens2f1", "ifName": "ens2f1", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "unknown", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "ens2f1", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eno5", "ifName": "eno5", "portName": null, "ifIndex": 4, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "eno5", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eno6", "ifName": "eno6", "portName": null, "ifIndex": 5, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "unknown", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "eno6", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "enp1s0f4u4", "ifName": "enp1s0f4u4", "portName": null, "ifIndex": 6, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "enp1s0f4u4", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "bond0", "ifName": "bond0", "portName": null, "ifIndex": 7, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "bond0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "enp1s0f4u4", "ifName": "enp1s0f4u4", "portName": null, "ifIndex": 8, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "enp1s0f4u4", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": 10000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 65536, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 46, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 46, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 2716, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 2716, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "ens2f0", "ifName": "ens2f0", "portName": null, "ifIndex": 2, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "ens2f0", "ifPhysAddress": "48df379ca8a0", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 53693802261, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 1254163, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 80480002185959, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 211750680, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 607873, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 40975, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "ens2f1", "ifName": "ens2f1", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": "true", "ifOperStatus": "unknown", "ifOperStatus_prev": "unknown", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "ens2f1", "ifPhysAddress": "48df37d783e8", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eno5", "ifName": "eno5", "portName": null, "ifIndex": 4, "ifSpeed": 10000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "eno5", "ifPhysAddress": "48df379ca8a0", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 29922425537, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 5142920537, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": **************, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 770379264563, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 101748, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 40967, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eno6", "ifName": "eno6", "portName": null, "ifIndex": 5, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": "true", "ifOperStatus": "unknown", "ifOperStatus_prev": "unknown", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "eno6", "ifPhysAddress": "48df379ca8a8", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "enp1s0f4u4", "ifName": "enp1s0f4u4", "portName": null, "ifIndex": 6, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "enp1s0f4u4", "ifPhysAddress": "0e9004b94984", "ifLastChange": 2977627197, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "bond0", "ifName": "bond0", "portName": null, "ifIndex": 7, "ifSpeed": 20000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "bond0", "ifPhysAddress": "48df379ca8a0", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 83616227798, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 5144174700, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 125242468718508, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 770591015243, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 710375, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 81942, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "enp1s0f4u4", "ifName": "enp1s0f4u4", "portName": null, "ifIndex": 8, "ifSpeed": null, "ifSpeed_prev": 0, "ifConnectorPresent": "true", "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "enp1s0f4u4", "ifPhysAddress": "0e9004b94984", "ifLastChange": 2977629599, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "processors": {"discovery": {"processors": [{"entPhysicalIndex": 0, "hrDeviceIndex": 196608, "processor_oid": ".*******.2.1.25.3.3.1.2.196608", "processor_index": "196608", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196609, "processor_oid": ".*******.2.1.25.3.3.1.2.196609", "processor_index": "196609", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196610, "processor_oid": ".*******.2.1.25.3.3.1.2.196610", "processor_index": "196610", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196611, "processor_oid": ".*******.2.1.25.3.3.1.2.196611", "processor_index": "196611", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196612, "processor_oid": ".*******.2.1.25.3.3.1.2.196612", "processor_index": "196612", "processor_type": "hr", "processor_usage": 0, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196613, "processor_oid": ".*******.2.1.25.3.3.1.2.196613", "processor_index": "196613", "processor_type": "hr", "processor_usage": 0, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196614, "processor_oid": ".*******.2.1.25.3.3.1.2.196614", "processor_index": "196614", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196615, "processor_oid": ".*******.2.1.25.3.3.1.2.196615", "processor_index": "196615", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196616, "processor_oid": ".*******.2.1.25.3.3.1.2.196616", "processor_index": "196616", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196617, "processor_oid": ".*******.2.1.25.3.3.1.2.196617", "processor_index": "196617", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196618, "processor_oid": ".*******.2.1.25.3.3.1.2.196618", "processor_index": "196618", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196619, "processor_oid": ".*******.2.1.25.3.3.1.2.196619", "processor_index": "196619", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196620, "processor_oid": ".*******.2.1.25.3.3.1.2.196620", "processor_index": "196620", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196621, "processor_oid": ".*******.2.1.25.3.3.1.2.196621", "processor_index": "196621", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196622, "processor_oid": ".*******.2.1.25.3.3.1.2.196622", "processor_index": "196622", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196623, "processor_oid": ".*******.2.1.25.3.3.1.2.196623", "processor_index": "196623", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196624, "processor_oid": ".*******.2.1.25.3.3.1.2.196624", "processor_index": "196624", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196625, "processor_oid": ".*******.2.1.25.3.3.1.2.196625", "processor_index": "196625", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196626, "processor_oid": ".*******.2.1.25.3.3.1.2.196626", "processor_index": "196626", "processor_type": "hr", "processor_usage": 0, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196627, "processor_oid": ".*******.2.1.25.3.3.1.2.196627", "processor_index": "196627", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196628, "processor_oid": ".*******.2.1.25.3.3.1.2.196628", "processor_index": "196628", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196629, "processor_oid": ".*******.2.1.25.3.3.1.2.196629", "processor_index": "196629", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196630, "processor_oid": ".*******.2.1.25.3.3.1.2.196630", "processor_index": "196630", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196631, "processor_oid": ".*******.2.1.25.3.3.1.2.196631", "processor_index": "196631", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196632, "processor_oid": ".*******.2.1.25.3.3.1.2.196632", "processor_index": "196632", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196633, "processor_oid": ".*******.2.1.25.3.3.1.2.196633", "processor_index": "196633", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196634, "processor_oid": ".*******.2.1.25.3.3.1.2.196634", "processor_index": "196634", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196635, "processor_oid": ".*******.2.1.25.3.3.1.2.196635", "processor_index": "196635", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196636, "processor_oid": ".*******.2.1.25.3.3.1.2.196636", "processor_index": "196636", "processor_type": "hr", "processor_usage": 0, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196637, "processor_oid": ".*******.2.1.25.3.3.1.2.196637", "processor_index": "196637", "processor_type": "hr", "processor_usage": 0, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196638, "processor_oid": ".*******.2.1.25.3.3.1.2.196638", "processor_index": "196638", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196639, "processor_oid": ".*******.2.1.25.3.3.1.2.196639", "processor_index": "196639", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196640, "processor_oid": ".*******.2.1.25.3.3.1.2.196640", "processor_index": "196640", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196641, "processor_oid": ".*******.2.1.25.3.3.1.2.196641", "processor_index": "196641", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196642, "processor_oid": ".*******.2.1.25.3.3.1.2.196642", "processor_index": "196642", "processor_type": "hr", "processor_usage": 0, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196643, "processor_oid": ".*******.2.1.25.3.3.1.2.196643", "processor_index": "196643", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196644, "processor_oid": ".*******.2.1.25.3.3.1.2.196644", "processor_index": "196644", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196645, "processor_oid": ".*******.2.1.25.3.3.1.2.196645", "processor_index": "196645", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196646, "processor_oid": ".*******.2.1.25.3.3.1.2.196646", "processor_index": "196646", "processor_type": "hr", "processor_usage": 0, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196647, "processor_oid": ".*******.2.1.25.3.3.1.2.196647", "processor_index": "196647", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196648, "processor_oid": ".*******.2.1.25.3.3.1.2.196648", "processor_index": "196648", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196649, "processor_oid": ".*******.2.1.25.3.3.1.2.196649", "processor_index": "196649", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196650, "processor_oid": ".*******.2.1.25.3.3.1.2.196650", "processor_index": "196650", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196651, "processor_oid": ".*******.2.1.25.3.3.1.2.196651", "processor_index": "196651", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196652, "processor_oid": ".*******.2.1.25.3.3.1.2.196652", "processor_index": "196652", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196653, "processor_oid": ".*******.2.1.25.3.3.1.2.196653", "processor_index": "196653", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196654, "processor_oid": ".*******.2.1.25.3.3.1.2.196654", "processor_index": "196654", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196655, "processor_oid": ".*******.2.1.25.3.3.1.2.196655", "processor_index": "196655", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196656, "processor_oid": ".*******.2.1.25.3.3.1.2.196656", "processor_index": "196656", "processor_type": "hr", "processor_usage": 0, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196657, "processor_oid": ".*******.2.1.25.3.3.1.2.196657", "processor_index": "196657", "processor_type": "hr", "processor_usage": 0, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196658, "processor_oid": ".*******.2.1.25.3.3.1.2.196658", "processor_index": "196658", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196659, "processor_oid": ".*******.2.1.25.3.3.1.2.196659", "processor_index": "196659", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196660, "processor_oid": ".*******.2.1.25.3.3.1.2.196660", "processor_index": "196660", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196661, "processor_oid": ".*******.2.1.25.3.3.1.2.196661", "processor_index": "196661", "processor_type": "hr", "processor_usage": 0, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196662, "processor_oid": ".*******.2.1.25.3.3.1.2.196662", "processor_index": "196662", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196663, "processor_oid": ".*******.2.1.25.3.3.1.2.196663", "processor_index": "196663", "processor_type": "hr", "processor_usage": 0, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196664, "processor_oid": ".*******.2.1.25.3.3.1.2.196664", "processor_index": "196664", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196665, "processor_oid": ".*******.2.1.25.3.3.1.2.196665", "processor_index": "196665", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196666, "processor_oid": ".*******.2.1.25.3.3.1.2.196666", "processor_index": "196666", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196667, "processor_oid": ".*******.2.1.25.3.3.1.2.196667", "processor_index": "196667", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196668, "processor_oid": ".*******.2.1.25.3.3.1.2.196668", "processor_index": "196668", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196669, "processor_oid": ".*******.2.1.25.3.3.1.2.196669", "processor_index": "196669", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196670, "processor_oid": ".*******.2.1.25.3.3.1.2.196670", "processor_index": "196670", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196671, "processor_oid": ".*******.2.1.25.3.3.1.2.196671", "processor_index": "196671", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196672, "processor_oid": ".*******.2.1.25.3.3.1.2.196672", "processor_index": "196672", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196673, "processor_oid": ".*******.2.1.25.3.3.1.2.196673", "processor_index": "196673", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196674, "processor_oid": ".*******.2.1.25.3.3.1.2.196674", "processor_index": "196674", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196675, "processor_oid": ".*******.2.1.25.3.3.1.2.196675", "processor_index": "196675", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196676, "processor_oid": ".*******.2.1.25.3.3.1.2.196676", "processor_index": "196676", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196677, "processor_oid": ".*******.2.1.25.3.3.1.2.196677", "processor_index": "196677", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196678, "processor_oid": ".*******.2.1.25.3.3.1.2.196678", "processor_index": "196678", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}, {"entPhysicalIndex": 0, "hrDeviceIndex": 196679, "processor_oid": ".*******.2.1.25.3.3.1.2.196679", "processor_index": "196679", "processor_type": "hr", "processor_usage": 1, "processor_descr": "Intel Xeon Gold 5220 @ 2.20GHz", "processor_precision": 1, "processor_perc_warn": 75}]}, "poller": "matches discovery"}, "mempools": {"discovery": {"mempools": [{"mempool_index": "0", "entPhysicalIndex": null, "mempool_type": "hpe-ilo", "mempool_class": "system", "mempool_precision": 1048576, "mempool_descr": "Physical Memory", "mempool_perc": 99, "mempool_perc_oid": null, "mempool_used": 134096093184, "mempool_used_oid": null, "mempool_free": 735051776, "mempool_free_oid": ".*******.*********1.2.13.2.0", "mempool_total": 134831144960, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 90}, {"mempool_index": "1", "entPhysicalIndex": null, "mempool_type": "hpe-ilo", "mempool_class": "system", "mempool_precision": 1048576, "mempool_descr": "Paging Memory", "mempool_perc": 0, "mempool_perc_oid": null, "mempool_used": 30408704, "mempool_used_oid": null, "mempool_free": 8558477312, "mempool_free_oid": ".*******.*********1.2.13.4.0", "mempool_total": 8588886016, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 90}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.7.0.1", "sensor_index": "cpqHeFltTolPowerSupplyBay.0.1", "sensor_type": "hpe-ilo", "sensor_descr": "PowerSupply #1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 86, "sensor_limit": 800, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.7.0.2", "sensor_index": "cpqHeFltTolPowerSupplyBay.0.2", "sensor_type": "hpe-ilo", "sensor_descr": "PowerSupply #2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 67, "sensor_limit": 800, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0", "sensor_index": "cpqDaAccelBattery.0", "sensor_type": "cpqDaAccelBattery", "sensor_descr": "Cache Module Board 0 Battery", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaAccelBattery"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.1", "sensor_index": "cpqDaAccelBattery.1", "sensor_type": "cpqDaAccelBattery", "sensor_descr": "<PERSON>ache Mo<PERSON>le Board 1 Battery", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaAccelBattery"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.6.0", "sensor_index": "cpqDaCntlrCondition.0", "sensor_type": "cpqDaCntlrCondition", "sensor_descr": "Array Controller Slot#0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaCntlrCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.6.1", "sensor_index": "cpqDaCntlrCondition.1", "sensor_type": "cpqDaCntlrCondition", "sensor_descr": "Array Controller Slot#1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaCntlrCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.11.0.1", "sensor_index": "cpqDaLogDrvCondition.0.1", "sensor_type": "cpqDaLogDrvCondition", "sensor_descr": "Logical Drive #1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.11.0.2", "sensor_index": "cpqDaLogDrvCondition.0.2", "sensor_type": "cpqDaLogDrvCondition", "sensor_descr": "Logical Drive #2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.11.0.3", "sensor_index": "cpqDaLogDrvCondition.0.3", "sensor_type": "cpqDaLogDrvCondition", "sensor_descr": "Logical Drive #3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.11.1.1", "sensor_index": "cpqDaLogDrvCondition.1.1", "sensor_type": "cpqDaLogDrvCondition", "sensor_descr": "Logical Drive #1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.11.1.2", "sensor_index": "cpqDaLogDrvCondition.1.2", "sensor_type": "cpqDaLogDrvCondition", "sensor_descr": "Logical Drive #2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.11.1.3", "sensor_index": "cpqDaLogDrvCondition.1.3", "sensor_type": "cpqDaLogDrvCondition", "sensor_descr": "Logical Drive #3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.11.1.4", "sensor_index": "cpqDaLogDrvCondition.1.4", "sensor_type": "cpqDaLogDrvCondition", "sensor_descr": "Logical Drive #4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.11.1.5", "sensor_index": "cpqDaLogDrvCondition.1.5", "sensor_type": "cpqDaLogDrvCondition", "sensor_descr": "Logical Drive #5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.11.1.6", "sensor_index": "cpqDaLogDrvCondition.1.6", "sensor_type": "cpqDaLogDrvCondition", "sensor_descr": "Logical Drive #6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.11.1.7", "sensor_index": "cpqDaLogDrvCondition.1.7", "sensor_type": "cpqDaLogDrvCondition", "sensor_descr": "Logical Drive #7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.1", "sensor_index": "cpqDaLogDrvIndex.0.1", "sensor_type": "cpqDaLogDrvStatus", "sensor_descr": "Logical Drive Status #1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.2", "sensor_index": "cpqDaLogDrvIndex.0.2", "sensor_type": "cpqDaLogDrvStatus", "sensor_descr": "Logical Drive Status #2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.3", "sensor_index": "cpqDaLogDrvIndex.0.3", "sensor_type": "cpqDaLogDrvStatus", "sensor_descr": "Logical Drive Status #3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.1.1", "sensor_index": "cpqDaLogDrvIndex.1.1", "sensor_type": "cpqDaLogDrvStatus", "sensor_descr": "Logical Drive Status #1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.1.2", "sensor_index": "cpqDaLogDrvIndex.1.2", "sensor_type": "cpqDaLogDrvStatus", "sensor_descr": "Logical Drive Status #2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.1.3", "sensor_index": "cpqDaLogDrvIndex.1.3", "sensor_type": "cpqDaLogDrvStatus", "sensor_descr": "Logical Drive Status #3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.1.4", "sensor_index": "cpqDaLogDrvIndex.1.4", "sensor_type": "cpqDaLogDrvStatus", "sensor_descr": "Logical Drive Status #4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.1.5", "sensor_index": "cpqDaLogDrvIndex.1.5", "sensor_type": "cpqDaLogDrvStatus", "sensor_descr": "Logical Drive Status #5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.1.6", "sensor_index": "cpqDaLogDrvIndex.1.6", "sensor_type": "cpqDaLogDrvStatus", "sensor_descr": "Logical Drive Status #6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.1.7", "sensor_index": "cpqDaLogDrvIndex.1.7", "sensor_type": "cpqDaLogDrvStatus", "sensor_descr": "Logical Drive Status #7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaLogDrvStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.0", "sensor_index": "cpqDaPhyDrvCondition.0.0", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1I:Box=1:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.1", "sensor_index": "cpqDaPhyDrvCondition.0.1", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1I:Box=1:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.16", "sensor_index": "cpqDaPhyDrvCondition.0.16", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=3I:Box=1:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.17", "sensor_index": "cpqDaPhyDrvCondition.0.17", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=3I:Box=1:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.18", "sensor_index": "cpqDaPhyDrvCondition.0.18", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=3I:Box=1:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.19", "sensor_index": "cpqDaPhyDrvCondition.0.19", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=3I:Box=1:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.2", "sensor_index": "cpqDaPhyDrvCondition.0.2", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1I:Box=1:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.20", "sensor_index": "cpqDaPhyDrvCondition.0.20", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=3I:Box=1:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.21", "sensor_index": "cpqDaPhyDrvCondition.0.21", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=3I:Box=1:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.22", "sensor_index": "cpqDaPhyDrvCondition.0.22", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=3I:Box=1:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.23", "sensor_index": "cpqDaPhyDrvCondition.0.23", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=3I:Box=1:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.24", "sensor_index": "cpqDaPhyDrvCondition.0.24", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=3I:Box=1:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.25", "sensor_index": "cpqDaPhyDrvCondition.0.25", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=3I:Box=1:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.26", "sensor_index": "cpqDaPhyDrvCondition.0.26", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=3I:Box=1:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.27", "sensor_index": "cpqDaPhyDrvCondition.0.27", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=3I:Box=1:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.3", "sensor_index": "cpqDaPhyDrvCondition.0.3", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1I:Box=1:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.4", "sensor_index": "cpqDaPhyDrvCondition.0.4", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=2I:Box=1:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.5", "sensor_index": "cpqDaPhyDrvCondition.0.5", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=2I:Box=1:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.6", "sensor_index": "cpqDaPhyDrvCondition.0.6", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=2I:Box=1:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.0.7", "sensor_index": "cpqDaPhyDrvCondition.0.7", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=2I:Box=1:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.0", "sensor_index": "cpqDaPhyDrvCondition.1.0", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=1:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.1", "sensor_index": "cpqDaPhyDrvCondition.1.1", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=1:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.10", "sensor_index": "cpqDaPhyDrvCondition.1.10", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=1:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.11", "sensor_index": "cpqDaPhyDrvCondition.1.11", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=1:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.12", "sensor_index": "cpqDaPhyDrvCondition.1.12", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=2:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.13", "sensor_index": "cpqDaPhyDrvCondition.1.13", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=2:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.14", "sensor_index": "cpqDaPhyDrvCondition.1.14", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=2:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.15", "sensor_index": "cpqDaPhyDrvCondition.1.15", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=2:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.16", "sensor_index": "cpqDaPhyDrvCondition.1.16", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=2:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.17", "sensor_index": "cpqDaPhyDrvCondition.1.17", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=2:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.18", "sensor_index": "cpqDaPhyDrvCondition.1.18", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=2:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.19", "sensor_index": "cpqDaPhyDrvCondition.1.19", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=2:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.2", "sensor_index": "cpqDaPhyDrvCondition.1.2", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=1:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.20", "sensor_index": "cpqDaPhyDrvCondition.1.20", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=2:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.21", "sensor_index": "cpqDaPhyDrvCondition.1.21", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=2:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.22", "sensor_index": "cpqDaPhyDrvCondition.1.22", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=2:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.23", "sensor_index": "cpqDaPhyDrvCondition.1.23", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=2:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.24", "sensor_index": "cpqDaPhyDrvCondition.1.24", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=3:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.25", "sensor_index": "cpqDaPhyDrvCondition.1.25", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=3:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.26", "sensor_index": "cpqDaPhyDrvCondition.1.26", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=3:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.27", "sensor_index": "cpqDaPhyDrvCondition.1.27", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=3:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.28", "sensor_index": "cpqDaPhyDrvCondition.1.28", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=3:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.29", "sensor_index": "cpqDaPhyDrvCondition.1.29", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=3:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.3", "sensor_index": "cpqDaPhyDrvCondition.1.3", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=1:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.30", "sensor_index": "cpqDaPhyDrvCondition.1.30", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=3:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.31", "sensor_index": "cpqDaPhyDrvCondition.1.31", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=3:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.32", "sensor_index": "cpqDaPhyDrvCondition.1.32", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=3:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.33", "sensor_index": "cpqDaPhyDrvCondition.1.33", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=3:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.34", "sensor_index": "cpqDaPhyDrvCondition.1.34", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=3:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.35", "sensor_index": "cpqDaPhyDrvCondition.1.35", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=3:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.36", "sensor_index": "cpqDaPhyDrvCondition.1.36", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=4:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.37", "sensor_index": "cpqDaPhyDrvCondition.1.37", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=4:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.38", "sensor_index": "cpqDaPhyDrvCondition.1.38", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=4:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.39", "sensor_index": "cpqDaPhyDrvCondition.1.39", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=4:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.4", "sensor_index": "cpqDaPhyDrvCondition.1.4", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=1:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.40", "sensor_index": "cpqDaPhyDrvCondition.1.40", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=4:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.41", "sensor_index": "cpqDaPhyDrvCondition.1.41", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=4:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.42", "sensor_index": "cpqDaPhyDrvCondition.1.42", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=4:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.43", "sensor_index": "cpqDaPhyDrvCondition.1.43", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=4:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.44", "sensor_index": "cpqDaPhyDrvCondition.1.44", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=4:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.45", "sensor_index": "cpqDaPhyDrvCondition.1.45", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=4:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.46", "sensor_index": "cpqDaPhyDrvCondition.1.46", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=4:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.47", "sensor_index": "cpqDaPhyDrvCondition.1.47", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=4:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.48", "sensor_index": "cpqDaPhyDrvCondition.1.48", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=5:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.49", "sensor_index": "cpqDaPhyDrvCondition.1.49", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=5:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.5", "sensor_index": "cpqDaPhyDrvCondition.1.5", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=1:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.50", "sensor_index": "cpqDaPhyDrvCondition.1.50", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=5:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.51", "sensor_index": "cpqDaPhyDrvCondition.1.51", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=5:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.52", "sensor_index": "cpqDaPhyDrvCondition.1.52", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=5:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.53", "sensor_index": "cpqDaPhyDrvCondition.1.53", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=5:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.54", "sensor_index": "cpqDaPhyDrvCondition.1.54", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=5:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.55", "sensor_index": "cpqDaPhyDrvCondition.1.55", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=5:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.56", "sensor_index": "cpqDaPhyDrvCondition.1.56", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=5:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.57", "sensor_index": "cpqDaPhyDrvCondition.1.57", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=5:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.58", "sensor_index": "cpqDaPhyDrvCondition.1.58", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=5:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.59", "sensor_index": "cpqDaPhyDrvCondition.1.59", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=5:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.6", "sensor_index": "cpqDaPhyDrvCondition.1.6", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=1:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.60", "sensor_index": "cpqDaPhyDrvCondition.1.60", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=6:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.61", "sensor_index": "cpqDaPhyDrvCondition.1.61", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=6:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.62", "sensor_index": "cpqDaPhyDrvCondition.1.62", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=6:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.63", "sensor_index": "cpqDaPhyDrvCondition.1.63", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=6:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.64", "sensor_index": "cpqDaPhyDrvCondition.1.64", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=6:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.65", "sensor_index": "cpqDaPhyDrvCondition.1.65", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=6:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.66", "sensor_index": "cpqDaPhyDrvCondition.1.66", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=6:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.67", "sensor_index": "cpqDaPhyDrvCondition.1.67", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=6:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.68", "sensor_index": "cpqDaPhyDrvCondition.1.68", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=6:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.69", "sensor_index": "cpqDaPhyDrvCondition.1.69", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=6:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.7", "sensor_index": "cpqDaPhyDrvCondition.1.7", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=1:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.70", "sensor_index": "cpqDaPhyDrvCondition.1.70", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=6:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.71", "sensor_index": "cpqDaPhyDrvCondition.1.71", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=6:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.72", "sensor_index": "cpqDaPhyDrvCondition.1.72", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=7:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.73", "sensor_index": "cpqDaPhyDrvCondition.1.73", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=7:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.74", "sensor_index": "cpqDaPhyDrvCondition.1.74", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=7:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.75", "sensor_index": "cpqDaPhyDrvCondition.1.75", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=7:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.76", "sensor_index": "cpqDaPhyDrvCondition.1.76", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=7:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.77", "sensor_index": "cpqDaPhyDrvCondition.1.77", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=7:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.78", "sensor_index": "cpqDaPhyDrvCondition.1.78", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=7:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.79", "sensor_index": "cpqDaPhyDrvCondition.1.79", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=7:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.8", "sensor_index": "cpqDaPhyDrvCondition.1.8", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=1:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.80", "sensor_index": "cpqDaPhyDrvCondition.1.80", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=7:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.81", "sensor_index": "cpqDaPhyDrvCondition.1.81", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=7:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.82", "sensor_index": "cpqDaPhyDrvCondition.1.82", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=7:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.83", "sensor_index": "cpqDaPhyDrvCondition.1.83", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=7:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.37.1.9", "sensor_index": "cpqDaPhyDrvCondition.1.9", "sensor_type": "cpqDaPhyDrvCondition", "sensor_descr": "Disk #Port=1E:Box=1:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqDaPhyDrvCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.1", "sensor_index": "cpqHeFltTolFanCondition.0.1", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.2", "sensor_index": "cpqHeFltTolFanCondition.0.2", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.3", "sensor_index": "cpqHeFltTolFanCondition.0.3", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.4", "sensor_index": "cpqHeFltTolFanCondition.0.4", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.5", "sensor_index": "cpqHeFltTolFanCondition.0.5", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.6", "sensor_index": "cpqHeFltTolFanCondition.0.6", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.7", "sensor_index": "cpqHeFltTolFanCondition.0.7", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.1", "sensor_index": "cpqHeFltTolPowerSupplyCondition.0.1", "sensor_type": "cpqHeFltTolPowerSupplyCondition", "sensor_descr": "PowerSupply #1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolPowerSupplyCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.2", "sensor_index": "cpqHeFltTolPowerSupplyCondition.0.2", "sensor_type": "cpqHeFltTolPowerSupplyCondition", "sensor_descr": "PowerSupply #2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolPowerSupplyCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.0", "sensor_index": "cpqHeResMem2ModuleCondition.0", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.1", "sensor_index": "cpqHeResMem2ModuleCondition.1", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.10", "sensor_index": "cpqHeResMem2ModuleCondition.10", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.11", "sensor_index": "cpqHeResMem2ModuleCondition.11", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.12", "sensor_index": "cpqHeResMem2ModuleCondition.12", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.13", "sensor_index": "cpqHeResMem2ModuleCondition.13", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.14", "sensor_index": "cpqHeResMem2ModuleCondition.14", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.15", "sensor_index": "cpqHeResMem2ModuleCondition.15", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.16", "sensor_index": "cpqHeResMem2ModuleCondition.16", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.17", "sensor_index": "cpqHeResMem2ModuleCondition.17", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.18", "sensor_index": "cpqHeResMem2ModuleCondition.18", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.19", "sensor_index": "cpqHeResMem2ModuleCondition.19", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.2", "sensor_index": "cpqHeResMem2ModuleCondition.2", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.20", "sensor_index": "cpqHeResMem2ModuleCondition.20", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.21", "sensor_index": "cpqHeResMem2ModuleCondition.21", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.22", "sensor_index": "cpqHeResMem2ModuleCondition.22", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.23", "sensor_index": "cpqHeResMem2ModuleCondition.23", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.3", "sensor_index": "cpqHeResMem2ModuleCondition.3", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.4", "sensor_index": "cpqHeResMem2ModuleCondition.4", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.5", "sensor_index": "cpqHeResMem2ModuleCondition.5", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.6", "sensor_index": "cpqHeResMem2ModuleCondition.6", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.7", "sensor_index": "cpqHeResMem2ModuleCondition.7", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.8", "sensor_index": "cpqHeResMem2ModuleCondition.8", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.9", "sensor_index": "cpqHeResMem2ModuleCondition.9", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.********.4.0.1", "sensor_index": "cpqHeSysBatteryCondition.0.1", "sensor_type": "cpqHeSysBatteryCondition", "sensor_descr": "Battery Condition (96W 875241-B21)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeSysBatteryCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.********.5.0.1", "sensor_index": "cpqHeSysBatteryStatus.0.1", "sensor_type": "cpqHeSysBatteryStatus", "sensor_descr": "Battery Status (96W 875241-B21)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeSysBatteryStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.6.0", "sensor_index": "cpqSeCpuStatus.0", "sensor_type": "cpqSeCpuStatus", "sensor_descr": "Processor #1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqSeCpuStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.6.1", "sensor_index": "cpqSeCpuStatus.1", "sensor_type": "cpqSeCpuStatus", "sensor_descr": "Processor #2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqSeCpuStatus"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.0", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.0", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1I:Box=1:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 46, "sensor_limit_warn": null, "sensor_limit_low": 16, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.1", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.1", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1I:Box=1:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29, "sensor_limit": 49, "sensor_limit_warn": null, "sensor_limit_low": 19, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.16", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.16", "sensor_type": "hpe-ilo", "sensor_descr": "Port=3I:Box=1:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 44, "sensor_limit": 64, "sensor_limit_warn": null, "sensor_limit_low": 34, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.17", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.17", "sensor_type": "hpe-ilo", "sensor_descr": "Port=3I:Box=1:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 44, "sensor_limit": 64, "sensor_limit_warn": null, "sensor_limit_low": 34, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.18", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.18", "sensor_type": "hpe-ilo", "sensor_descr": "Port=3I:Box=1:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 43, "sensor_limit": 63, "sensor_limit_warn": null, "sensor_limit_low": 33, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.19", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.19", "sensor_type": "hpe-ilo", "sensor_descr": "Port=3I:Box=1:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 42, "sensor_limit": 62, "sensor_limit_warn": null, "sensor_limit_low": 32, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.2", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.2", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1I:Box=1:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 27, "sensor_limit": 47, "sensor_limit_warn": null, "sensor_limit_low": 17, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.20", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.20", "sensor_type": "hpe-ilo", "sensor_descr": "Port=3I:Box=1:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 60, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.21", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.21", "sensor_type": "hpe-ilo", "sensor_descr": "Port=3I:Box=1:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 42, "sensor_limit": 62, "sensor_limit_warn": null, "sensor_limit_low": 32, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.22", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.22", "sensor_type": "hpe-ilo", "sensor_descr": "Port=3I:Box=1:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 42, "sensor_limit": 62, "sensor_limit_warn": null, "sensor_limit_low": 32, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.23", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.23", "sensor_type": "hpe-ilo", "sensor_descr": "Port=3I:Box=1:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 41, "sensor_limit": 61, "sensor_limit_warn": null, "sensor_limit_low": 31, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.24", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.24", "sensor_type": "hpe-ilo", "sensor_descr": "Port=3I:Box=1:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 42, "sensor_limit": 62, "sensor_limit_warn": null, "sensor_limit_low": 32, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.25", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.25", "sensor_type": "hpe-ilo", "sensor_descr": "Port=3I:Box=1:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 60, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.26", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.26", "sensor_type": "hpe-ilo", "sensor_descr": "Port=3I:Box=1:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 42, "sensor_limit": 62, "sensor_limit_warn": null, "sensor_limit_low": 32, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.27", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.27", "sensor_type": "hpe-ilo", "sensor_descr": "Port=3I:Box=1:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 42, "sensor_limit": 62, "sensor_limit_warn": null, "sensor_limit_low": 32, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.3", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.3", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1I:Box=1:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 48, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.4", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.4", "sensor_type": "hpe-ilo", "sensor_descr": "Port=2I:Box=1:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 24, "sensor_limit": 44, "sensor_limit_warn": null, "sensor_limit_low": 14, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.5", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.5", "sensor_type": "hpe-ilo", "sensor_descr": "Port=2I:Box=1:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 27, "sensor_limit": 47, "sensor_limit_warn": null, "sensor_limit_low": 17, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.6", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.6", "sensor_type": "hpe-ilo", "sensor_descr": "Port=2I:Box=1:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 48, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.0.7", "sensor_index": "cpqDaPhyDrvCurrentTemperature.0.7", "sensor_type": "hpe-ilo", "sensor_descr": "Port=2I:Box=1:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29, "sensor_limit": 49, "sensor_limit_warn": null, "sensor_limit_low": 19, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.0", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.0", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=1:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.1", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.1", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=1:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.10", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.10", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=1:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.11", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.11", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=1:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.12", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.12", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=2:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.13", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.13", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=2:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.14", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.14", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=2:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.15", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.15", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=2:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.16", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.16", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=2:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.17", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.17", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=2:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.18", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.18", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=2:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.19", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.19", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=2:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29, "sensor_limit": 49, "sensor_limit_warn": null, "sensor_limit_low": 19, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.2", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.2", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=1:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.20", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.20", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=2:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29, "sensor_limit": 49, "sensor_limit_warn": null, "sensor_limit_low": 19, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.21", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.21", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=2:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.22", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.22", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=2:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.23", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.23", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=2:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.24", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.24", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=3:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 33, "sensor_limit": 53, "sensor_limit_warn": null, "sensor_limit_low": 23, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.25", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.25", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=3:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.26", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.26", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=3:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.27", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.27", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=3:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.28", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.28", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=3:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.29", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.29", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=3:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.3", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.3", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=1:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.30", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.30", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=3:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.31", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.31", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=3:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.32", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.32", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=3:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.33", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.33", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=3:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.34", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.34", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=3:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.35", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.35", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=3:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.36", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.36", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=4:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 34, "sensor_limit": 54, "sensor_limit_warn": null, "sensor_limit_low": 24, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.37", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.37", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=4:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.38", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.38", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=4:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.39", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.39", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=4:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.4", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.4", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=1:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29, "sensor_limit": 49, "sensor_limit_warn": null, "sensor_limit_low": 19, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.40", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.40", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=4:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.41", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.41", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=4:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.42", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.42", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=4:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.43", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.43", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=4:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.44", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.44", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=4:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.45", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.45", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=4:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.46", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.46", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=4:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.47", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.47", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=4:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.48", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.48", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=5:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 33, "sensor_limit": 53, "sensor_limit_warn": null, "sensor_limit_low": 23, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.49", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.49", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=5:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.5", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.5", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=1:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.50", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.50", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=5:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 33, "sensor_limit": 53, "sensor_limit_warn": null, "sensor_limit_low": 23, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.51", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.51", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=5:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.52", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.52", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=5:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.53", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.53", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=5:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.54", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.54", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=5:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.55", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.55", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=5:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.56", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.56", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=5:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.57", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.57", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=5:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.58", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.58", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=5:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.59", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.59", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=5:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.6", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.6", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=1:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29, "sensor_limit": 49, "sensor_limit_warn": null, "sensor_limit_low": 19, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.60", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.60", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=6:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.61", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.61", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=6:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.62", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.62", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=6:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.63", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.63", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=6:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.64", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.64", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=6:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.65", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.65", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=6:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.66", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.66", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=6:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.67", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.67", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=6:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.68", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.68", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=6:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.69", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.69", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=6:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.7", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.7", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=1:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.70", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.70", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=6:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.71", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.71", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=6:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.72", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.72", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=7:Bay=1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 32, "sensor_limit": 52, "sensor_limit_warn": null, "sensor_limit_low": 22, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.73", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.73", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=7:Bay=2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.74", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.74", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=7:Bay=3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.75", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.75", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=7:Bay=4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.76", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.76", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=7:Bay=5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.77", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.77", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=7:Bay=6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.78", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.78", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=7:Bay=7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.79", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.79", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=7:Bay=8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.8", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.8", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=1:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29, "sensor_limit": 49, "sensor_limit_warn": null, "sensor_limit_low": 19, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.80", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.80", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=7:Bay=9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29, "sensor_limit": 49, "sensor_limit_warn": null, "sensor_limit_low": 19, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.81", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.81", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=7:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.82", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.82", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=7:Bay=11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.83", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.83", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=7:Bay=12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 50, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.70.1.9", "sensor_index": "cpqDaPhyDrvCurrentTemperature.1.9", "sensor_type": "hpe-ilo", "sensor_descr": "Port=1E:Box=1:Bay=10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.1", "sensor_index": "cpqHeTemperatureCelsius.0.1", "sensor_type": "hpe-ilo", "sensor_descr": "ambient", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.10", "sensor_index": "cpqHeTemperatureCelsius.0.10", "sensor_type": "hpe-ilo", "sensor_descr": "memory", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.12", "sensor_index": "cpqHeTemperatureCelsius.0.12", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 37, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 27, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.14", "sensor_index": "cpqHeTemperatureCelsius.0.14", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.15", "sensor_index": "cpqHeTemperatureCelsius.0.15", "sensor_type": "hpe-ilo", "sensor_descr": "ambient", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 23, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 13, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.16", "sensor_index": "cpqHeTemperatureCelsius.0.16", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.17", "sensor_index": "cpqHeTemperatureCelsius.0.17", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 19, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.18", "sensor_index": "cpqHeTemperatureCelsius.0.18", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 16, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.19", "sensor_index": "cpqHeTemperatureCelsius.0.19", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.2", "sensor_index": "cpqHeTemperatureCelsius.0.2", "sensor_type": "hpe-ilo", "sensor_descr": "cpu", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.20", "sensor_index": "cpqHeTemperatureCelsius.0.20", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 27, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 17, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.21", "sensor_index": "cpqHeTemperatureCelsius.0.21", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.22", "sensor_index": "cpqHeTemperatureCelsius.0.22", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.23", "sensor_index": "cpqHeTemperatureCelsius.0.23", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 58, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 48, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.24", "sensor_index": "cpqHeTemperatureCelsius.0.24", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.25", "sensor_index": "cpqHeTemperatureCelsius.0.25", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 48, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 38, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.26", "sensor_index": "cpqHeTemperatureCelsius.0.26", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 27, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 17, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.27", "sensor_index": "cpqHeTemperatureCelsius.0.27", "sensor_type": "hpe-ilo", "sensor_descr": "ioBoard", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 66, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 56, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.28", "sensor_index": "cpqHeTemperatureCelsius.0.28", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 16, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.29", "sensor_index": "cpqHeTemperatureCelsius.0.29", "sensor_type": "hpe-ilo", "sensor_descr": "ioBoard", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 52, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 42, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.3", "sensor_index": "cpqHeTemperatureCelsius.0.3", "sensor_type": "hpe-ilo", "sensor_descr": "cpu", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.30", "sensor_index": "cpqHeTemperatureCelsius.0.30", "sensor_type": "hpe-ilo", "sensor_descr": "ioBoard", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 16, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.31", "sensor_index": "cpqHeTemperatureCelsius.0.31", "sensor_type": "hpe-ilo", "sensor_descr": "ioBoard", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 61, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 51, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.32", "sensor_index": "cpqHeTemperatureCelsius.0.32", "sensor_type": "hpe-ilo", "sensor_descr": "ioBoard", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.34", "sensor_index": "cpqHeTemperatureCelsius.0.34", "sensor_type": "hpe-ilo", "sensor_descr": "ioBoard", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 16, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.36", "sensor_index": "cpqHeTemperatureCelsius.0.36", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 27, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 17, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.37", "sensor_index": "cpqHeTemperatureCelsius.0.37", "sensor_type": "hpe-ilo", "sensor_descr": "powerSupply", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.38", "sensor_index": "cpqHeTemperatureCelsius.0.38", "sensor_type": "hpe-ilo", "sensor_descr": "powerSupply", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 26, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 16, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.39", "sensor_index": "cpqHeTemperatureCelsius.0.39", "sensor_type": "hpe-ilo", "sensor_descr": "powerSupply", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.40", "sensor_index": "cpqHeTemperatureCelsius.0.40", "sensor_type": "hpe-ilo", "sensor_descr": "powerSupply", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.41", "sensor_index": "cpqHeTemperatureCelsius.0.41", "sensor_type": "hpe-ilo", "sensor_descr": "powerSupply", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 23, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 13, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.42", "sensor_index": "cpqHeTemperatureCelsius.0.42", "sensor_type": "hpe-ilo", "sensor_descr": "powerSupply", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.43", "sensor_index": "cpqHeTemperatureCelsius.0.43", "sensor_type": "hpe-ilo", "sensor_descr": "cpu", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.44", "sensor_index": "cpqHeTemperatureCelsius.0.44", "sensor_type": "hpe-ilo", "sensor_descr": "cpu", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 29, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 19, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.6", "sensor_index": "cpqHeTemperatureCelsius.0.6", "sensor_type": "hpe-ilo", "sensor_descr": "memory", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 25, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 15, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "cpqDaAccelBattery", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqDaAccelBattery", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqDaAccelBattery", "state_descr": "recharging", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "cpqDaAccelBattery", "state_descr": "failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "cpqDaAccelBattery", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 1}, {"state_name": "cpqDaAccelBattery", "state_descr": "notPresent", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 3}, {"state_name": "cpqDaAccelBattery", "state_descr": "capacitorFailed", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 1}, {"state_name": "cpqDaCntlrCondition", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqDaCntlrCondition", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqDaCntlrCondition", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "cpqDaCntlrCondition", "state_descr": "failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvCondition", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqDaLogDrvCondition", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqDaLogDrvCondition", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvCondition", "state_descr": "failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "failed", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "unconfigured", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 3}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "recovering", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "readyRebuild", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "rebuilding", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 2}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "wrongDrive", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 2}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "badConnect", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "overheating", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 2}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "shutdown", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "expanding", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "notAvailable", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 3}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "queuedForExpansion", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "multipathAccessDegraded", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "erasing", "state_draw_graph": 1, "state_value": 16, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "predictiveSpareRebuildReady", "state_draw_graph": 1, "state_value": 17, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "rapidParityInitializationInProgress", "state_draw_graph": 1, "state_value": 18, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "rapidParityInitializationPending", "state_draw_graph": 1, "state_value": 19, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "noAccessEncryptedWithNoControllerKey", "state_draw_graph": 1, "state_value": 20, "state_generic_value": 0}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "unencryptedToEncryptedTransformationInProgress", "state_draw_graph": 1, "state_value": 21, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "newLogicalDriveKeyRekeyInProgress", "state_draw_graph": 1, "state_value": 22, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "noAccessEncryptedWithControllerEncryptionNotEnabled", "state_draw_graph": 1, "state_value": 23, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "unencryptedToEncryptedTransformationNotStarted", "state_draw_graph": 1, "state_value": 24, "state_generic_value": 1}, {"state_name": "cpqDaLogDrvStatus", "state_descr": "newLogicalDriveKeyRekeyRequestReceived", "state_draw_graph": 1, "state_value": 25, "state_generic_value": 1}, {"state_name": "cpqDaPhyDrvCondition", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqDaPhyDrvCondition", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqDaPhyDrvCondition", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "cpqDaPhyDrvCondition", "state_descr": "failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 2}, {"state_name": "cpqHeFltTolFanCondition", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqHeFltTolFanCondition", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqHeFltTolFanCondition", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "cpqHeFltTolFanCondition", "state_descr": "failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "cpqHeFltTolPowerSupplyCondition", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqHeFltTolPowerSupplyCondition", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqHeFltTolPowerSupplyCondition", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "cpqHeFltTolPowerSupplyCondition", "state_descr": "failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "cpqHeResMem2ModuleCondition", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqHeResMem2ModuleCondition", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqHeResMem2ModuleCondition", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "cpqHeResMem2ModuleCondition", "state_descr": "degradedModuleIndexUnknown", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "cpqHeSysBatteryCondition", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqHeSysBatteryCondition", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqHeSysBatteryCondition", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryCondition", "state_descr": "failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "generalFailure", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownHighResistance", "state_draw_graph": 3, "state_value": 3, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownLowVoltage", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownShortCircuit", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownChargeTimeout", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownOverTemperature", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownDischargeMinVoltage", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownDischargeCurrent", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownLoadCountHigh", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownEnablePin", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownOverCurrent", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownPermanentFailure", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownBackupTimeExceeded", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "predictiveFailure", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 2}, {"state_name": "cpqSeCpuStatus", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqSeCpuStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqSeCpuStatus", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "cpqSeCpuStatus", "state_descr": "failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}]}, "poller": "matches discovery"}, "storage": {"discovery": {"storage": [{"type": "hpe-ilo", "storage_index": "30", "storage_type": "Storage", "storage_descr": "/dev/mapper/os-opt on /opt", "storage_size": 32196526080, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 167772160, "storage_used_oid": ".*******.*********1.2.4.1.1.4.30", "storage_free": 32028753920, "storage_free_oid": null, "storage_perc": 1, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "31", "storage_type": "Storage", "storage_descr": "/dev/mapper/mount1 on /mount1", "storage_size": 1199274196992, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 1231028224, "storage_used_oid": ".*******.*********1.2.4.1.1.4.31", "storage_free": 1198043168768, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "32", "storage_type": "Storage", "storage_descr": "/dev/sdi2 on /boot", "storage_size": 1020264448, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 123731968, "storage_used_oid": ".*******.*********1.2.4.1.1.4.32", "storage_free": 896532480, "storage_free_oid": null, "storage_perc": 12, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "33", "storage_type": "Storage", "storage_descr": "/dev/mapper/mount2 on /mount2", "storage_size": 360066232352770, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 281139614842880, "storage_used_oid": ".*******.*********1.2.4.1.1.4.33", "storage_free": 78926617509888, "storage_free_oid": null, "storage_perc": 78, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "34", "storage_type": "Storage", "storage_descr": "/dev/sdi1 on /boot/efi", "storage_size": 509607936, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 4194304, "storage_used_oid": ".*******.*********1.2.4.1.1.4.34", "storage_free": 505413632, "storage_free_oid": null, "storage_perc": 1, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "35", "storage_type": "Storage", "storage_descr": "/dev/mapper/os-var on /var", "storage_size": 39918239744, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 1869611008, "storage_used_oid": ".*******.*********1.2.4.1.1.4.35", "storage_free": 38048628736, "storage_free_oid": null, "storage_perc": 5, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "37", "storage_type": "Storage", "storage_descr": "/dev/sdk1 on /mount3", "storage_size": 40005378506752, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 34760661401600, "storage_used_oid": ".*******.*********1.2.4.1.1.4.37", "storage_free": 5244717105152, "storage_free_oid": null, "storage_perc": 87, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "5", "storage_type": "Storage", "storage_descr": "/dev/mapper/os-root on /", "storage_size": 26829914112, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 102760448, "storage_used_oid": ".*******.*********1.2.4.1.1.4.5", "storage_free": 26727153664, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "6", "storage_type": "Storage", "storage_descr": "/dev/mapper/os-usr on /usr", "storage_size": 37562089472, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 2232418304, "storage_used_oid": ".*******.*********1.2.4.1.1.4.6", "storage_free": 35329671168, "storage_free_oid": null, "storage_perc": 6, "storage_perc_oid": null, "storage_perc_warn": 60}]}, "poller": {"storage": [{"type": "hpe-ilo", "storage_index": "30", "storage_type": "Storage", "storage_descr": "/dev/mapper/os-opt on /opt", "storage_size": 32196526080, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 167772160, "storage_used_oid": ".*******.*********1.2.4.1.1.4.30", "storage_free": 32028753920, "storage_free_oid": null, "storage_perc": 1, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "31", "storage_type": "Storage", "storage_descr": "/dev/mapper/mount1 on /mount1", "storage_size": 1199274196992, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 1231028224, "storage_used_oid": ".*******.*********1.2.4.1.1.4.31", "storage_free": 1198043168768, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "32", "storage_type": "Storage", "storage_descr": "/dev/sdi2 on /boot", "storage_size": 1020264448, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 123731968, "storage_used_oid": ".*******.*********1.2.4.1.1.4.32", "storage_free": 896532480, "storage_free_oid": null, "storage_perc": 12, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "33", "storage_type": "Storage", "storage_descr": "/dev/mapper/mount2 on /mount2", "storage_size": 360066232352770, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 281139614842880, "storage_used_oid": ".*******.*********1.2.4.1.1.4.33", "storage_free": 78926617509890, "storage_free_oid": null, "storage_perc": 78, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "34", "storage_type": "Storage", "storage_descr": "/dev/sdi1 on /boot/efi", "storage_size": 509607936, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 4194304, "storage_used_oid": ".*******.*********1.2.4.1.1.4.34", "storage_free": 505413632, "storage_free_oid": null, "storage_perc": 1, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "35", "storage_type": "Storage", "storage_descr": "/dev/mapper/os-var on /var", "storage_size": 39918239744, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 1869611008, "storage_used_oid": ".*******.*********1.2.4.1.1.4.35", "storage_free": 38048628736, "storage_free_oid": null, "storage_perc": 5, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "37", "storage_type": "Storage", "storage_descr": "/dev/sdk1 on /mount3", "storage_size": 40005378506752, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 34760661401600, "storage_used_oid": ".*******.*********1.2.4.1.1.4.37", "storage_free": 5244717105152, "storage_free_oid": null, "storage_perc": 87, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "5", "storage_type": "Storage", "storage_descr": "/dev/mapper/os-root on /", "storage_size": 26829914112, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 102760448, "storage_used_oid": ".*******.*********1.2.4.1.1.4.5", "storage_free": 26727153664, "storage_free_oid": null, "storage_perc": 0, "storage_perc_oid": null, "storage_perc_warn": 60}, {"type": "hpe-ilo", "storage_index": "6", "storage_type": "Storage", "storage_descr": "/dev/mapper/os-usr on /usr", "storage_size": 37562089472, "storage_size_oid": null, "storage_units": 1048576, "storage_used": 2232418304, "storage_used_oid": ".*******.*********1.2.4.1.1.4.6", "storage_free": 35329671168, "storage_free_oid": null, "storage_perc": 6, "storage_perc_oid": null, "storage_perc_warn": 60}]}}}