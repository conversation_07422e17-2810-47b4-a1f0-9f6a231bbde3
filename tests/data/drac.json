{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.674.10892.5", "sysDescr": "Dell Out-of-band SNMP Agent for Remote Access Controller", "sysContact": "<private>", "version": "6.20.200.201810113973", "hardware": "PowerEdge M1000e", "features": null, "location": "<private>", "os": "drac", "type": "appliance", "serial": "1234ABC", "icon": "dell.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eth0", "ifName": "eth0", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "eth0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "bond0", "ifName": "bond0", "portName": null, "ifIndex": 6, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "bond0", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "lo", "ifName": "lo", "portName": null, "ifIndex": 1, "ifSpeed": 10000000, "ifSpeed_prev": null, "ifConnectorPresent": "false", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 65536, "ifType": "softwareLoopback", "ifAlias": "lo", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 16988518, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 16988518, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 1456038490, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 1456038490, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "eth0", "ifName": "eth0", "portName": null, "ifIndex": 3, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "eth0", "ifPhysAddress": "90b11c2cc8af", "ifLastChange": 1507, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 31174529, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 5270399, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 4305376423, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 1263844147, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "bond0", "ifName": "bond0", "portName": null, "ifIndex": 6, "ifSpeed": 1000000000, "ifSpeed_prev": null, "ifConnectorPresent": "true", "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "bond0", "ifPhysAddress": "f8bc12520b6c", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 19380, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 10587, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 1, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 2496931, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 3502636, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 1, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.14.1", "sensor_index": "1", "sensor_type": "drac", "sensor_descr": "Chassis 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7.744, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.1", "sensor_index": "1.1", "sensor_type": "drac", "sensor_descr": "PS1 Current 1", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 0.8, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.2", "sensor_index": "1.2", "sensor_type": "drac", "sensor_descr": "PS2 Current 2", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.6.1.3", "sensor_index": "1.3", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2.094, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.6.1.4", "sensor_index": "1.4", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1.65, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.6.1.5", "sensor_index": "1.5", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1.523, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.6.1.6", "sensor_index": "1.6", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.1", "sensor_index": "1", "sensor_type": "drac", "sensor_descr": "System Fan1A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4440, "sensor_limit": 7992, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.10", "sensor_index": "10", "sensor_type": "drac", "sensor_descr": "System Fan5B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3720, "sensor_limit": 6696, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.11", "sensor_index": "11", "sensor_type": "drac", "sensor_descr": "System Fan6A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4560, "sensor_limit": 8208, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.12", "sensor_index": "12", "sensor_type": "drac", "sensor_descr": "System Fan6B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3480, "sensor_limit": 6264, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.2", "sensor_index": "2", "sensor_type": "drac", "sensor_descr": "System Fan1B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3240, "sensor_limit": 5832, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.3", "sensor_index": "3", "sensor_type": "drac", "sensor_descr": "System Fan2A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3720, "sensor_limit": 6696, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.4", "sensor_index": "4", "sensor_type": "drac", "sensor_descr": "System Fan2B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3360, "sensor_limit": 6048, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.5", "sensor_index": "5", "sensor_type": "drac", "sensor_descr": "System Fan3A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4560, "sensor_limit": 8208, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.6", "sensor_index": "6", "sensor_type": "drac", "sensor_descr": "System Fan3B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4080, "sensor_limit": 7344, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.7", "sensor_index": "7", "sensor_type": "drac", "sensor_descr": "System Fan4A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4560, "sensor_limit": 8208, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.8", "sensor_index": "8", "sensor_type": "drac", "sensor_descr": "System Fan4B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4080, "sensor_limit": 7344, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.9", "sensor_index": "9", "sensor_type": "drac", "sensor_descr": "System Fan5A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3840, "sensor_limit": 6912, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.3", "sensor_index": "1.3", "sensor_type": "drac", "sensor_descr": "System Board Pwr Consumption", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 168, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.3.1", "sensor_index": "drsIdlePower.1", "sensor_type": "drac", "sensor_descr": "Chassis 1 Idle Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1721, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.4.1", "sensor_index": "drsMaxPowerSpecification.1", "sensor_type": "drac", "sensor_descr": "Chassis 1 Max Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 12514, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.2.1", "sensor_index": "drsPotentialPower.1", "sensor_type": "drac", "sensor_descr": "Chassis 1 Potential Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6459, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1", "sensor_index": "drsPowerSurplus.1", "sensor_type": "drac", "sensor_descr": "Chassis 1 Surplus Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6055, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.8.1", "sensor_index": "drsWattsPeakUsage.1", "sensor_type": "drac", "sensor_descr": "Chassis 1 Peak Usage", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4803, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.13.1", "sensor_index": "drsWattsReading.1", "sensor_type": "drac", "sensor_descr": "Chassis 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1721, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.1", "sensor_index": "1.1", "sensor_type": "amperageProbeStatus", "sensor_descr": "PS1 Current 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.1", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "amperageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.2", "sensor_index": "1.2", "sensor_type": "amperageProbeStatus", "sensor_descr": "PS2 Current 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.2", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "amperageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.3", "sensor_index": "1.3", "sensor_type": "amperageProbeStatus", "sensor_descr": "System Board Pwr Consumption", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.3", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "amperageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.**********.0", "sensor_index": "controllerComponentStatus.0", "sensor_type": "DELL-RAC-MIB::controllerTable", "sensor_descr": "Shared PERC8 (Embedded) Chassis Integrated RAID 2  State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::controllerTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.**********.2", "sensor_index": "controllerComponentStatus.2", "sensor_type": "DELL-RAC-MIB::controllerTable", "sensor_descr": "Shared PERC8 (Embedded) Chassis Integrated RAID 1  State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::controllerTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.0", "sensor_index": "physicalDiskStatus.0", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:4 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.1", "sensor_index": "physicalDiskStatus.1", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:9 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.10", "sensor_index": "physicalDiskStatus.10", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:13 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.11", "sensor_index": "physicalDiskStatus.11", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:17 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.12", "sensor_index": "physicalDiskStatus.12", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:16 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.13", "sensor_index": "physicalDiskStatus.13", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:18 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.14", "sensor_index": "physicalDiskStatus.14", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:15 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.15", "sensor_index": "physicalDiskStatus.15", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:19 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.16", "sensor_index": "physicalDiskStatus.16", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:20 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.17", "sensor_index": "physicalDiskStatus.17", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:24 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.18", "sensor_index": "physicalDiskStatus.18", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:21 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.19", "sensor_index": "physicalDiskStatus.19", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:2 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.2", "sensor_index": "physicalDiskStatus.2", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:6 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.20", "sensor_index": "physicalDiskStatus.20", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:1 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.21", "sensor_index": "physicalDiskStatus.21", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:3 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.22", "sensor_index": "physicalDiskStatus.22", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:0 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.23", "sensor_index": "physicalDiskStatus.23", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:23 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.24", "sensor_index": "physicalDiskStatus.24", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:22 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.3", "sensor_index": "physicalDiskStatus.3", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:7 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.4", "sensor_index": "physicalDiskStatus.4", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:8 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.5", "sensor_index": "physicalDiskStatus.5", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:5 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.6", "sensor_index": "physicalDiskStatus.6", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:10 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.7", "sensor_index": "physicalDiskStatus.7", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:14 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.8", "sensor_index": "physicalDiskStatus.8", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:11 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.9", "sensor_index": "physicalDiskStatus.9", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:12 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsBladeCurrStatus.0", "sensor_type": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "Blade Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsCMCCurrStatus.0", "sensor_type": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "CMC Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.2.1", "sensor_index": "drsServerMonitoringCapable.1", "sensor_type": "drsCMCServerTable", "sensor_descr": "Slot 1 SLOT-01 PowerEdge M640 (VRTX) State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsCMCServerTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.2.3", "sensor_index": "drsServerMonitoringCapable.3", "sensor_type": "drsCMCServerTable", "sensor_descr": "Slot 3 SLOT-03 N/A State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsCMCServerTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.2.5", "sensor_index": "drsServerMonitoringCapable.5", "sensor_type": "drsCMCServerTable", "sensor_descr": "Slot 2 SLOT-02 PowerEdge M640 (VRTX) State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsCMCServerTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.2.7", "sensor_index": "drsServerMonitoringCapable.7", "sensor_type": "drsCMCServerTable", "sensor_descr": "Slot 4 SLOT-04 N/A State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsCMCServerTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsFanCurrStatus.0", "sensor_type": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "FAN Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******", "sensor_index": "drsGlobalSystemStatus.0", "sensor_type": "drsGlobalSystemStatus", "sensor_descr": "Global System Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsGlobalSystemStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsIOMCurrStatus.0", "sensor_type": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "IOM Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsKVMCurrStatus.0", "sensor_type": "drsKVMCurrStatus", "sensor_descr": "KVM Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsKVMCurrStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsPowerCurrStatus.0", "sensor_type": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "sensor_descr": "Power Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsRedCurrStatus.0", "sensor_type": "drsRedCurrStatus", "sensor_descr": "Redundancy Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsRedCurrStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsTempCurrStatus.0", "sensor_type": "drsTemp<PERSON>urr<PERSON><PERSON>us", "sensor_descr": "Temperature Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.300.********.1", "sensor_index": "intrusionReading.1.1", "sensor_type": "intrusionReading", "sensor_descr": "Intrusion Reading", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "intrusionReading"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.300.********.1", "sensor_index": "intrusionStatus.1.1", "sensor_type": "intrusionStatus", "sensor_descr": "Intrusion Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "intrusionStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.1", "sensor_index": "1.1", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.A1, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.1", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.2", "sensor_index": "1.2", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.A2, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.2", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.3", "sensor_index": "1.3", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.A3, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.3", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.4", "sensor_index": "1.4", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.A4, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.4", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.5", "sensor_index": "1.5", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.B1, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.5", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.6", "sensor_index": "1.6", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.B2, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.6", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.7", "sensor_index": "1.7", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.B3, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.7", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.8", "sensor_index": "1.8", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.B4, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.8", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.1", "sensor_index": "1.1", "sensor_type": "processorDeviceStatus", "sensor_descr": "Intel(R) Xeon(R) CPU E5-2620 v3 @ 2.40GHz", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.1", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "processorDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.2", "sensor_index": "1.2", "sensor_type": "processorDeviceStatus", "sensor_descr": "Intel(R) Xeon(R) CPU E5-2620 v3 @ 2.40GHz", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.2", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "processorDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.1", "sensor_index": "1.1", "sensor_type": "systemBatteryStatus", "sensor_descr": "System Board CMOS Battery", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.1", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "systemBatteryStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.2", "sensor_index": "1.2", "sensor_type": "systemBatteryStatus", "sensor_descr": "PERC1 ROMB Battery", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.2", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "systemBatteryStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.**********.1.60.1", "sensor_index": "systemStateIDSDMCardDeviceStatusCombined.1", "sensor_type": "systemStateIDSDMCardDeviceStatusCombined", "sensor_descr": "IDSDM Card Device Combined Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "systemStateIDSDMCardDeviceStatusCombined"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.**********.1.58.1", "sensor_index": "systemStateIDSDMCardUnitStatusCombined.1", "sensor_type": "systemStateIDSDMCardUnitStatusCombined", "sensor_descr": "IDSDM Card Unit Combined Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "systemStateIDSDMCardUnitStatusCombined"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.1", "sensor_index": "1", "sensor_type": "virtualDiskState", "sensor_descr": "Virtual Disk 0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "virtualDiskState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.1", "sensor_index": "1.1", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 VCORE PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.1", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.10", "sensor_index": "1.10", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 1.05V PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.10", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.11", "sensor_index": "1.11", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 2.5V AUX PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.11", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.12", "sensor_index": "1.12", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 5V SWITCH PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.12", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.13", "sensor_index": "1.13", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board BP1 5V PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.13", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.14", "sensor_index": "1.14", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board PS1 PG Fail", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.14", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.15", "sensor_index": "1.15", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board PS2 PG Fail", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.15", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.16", "sensor_index": "1.16", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 DIMM PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.16", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.17", "sensor_index": "1.17", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 VCCIO PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.17", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.18", "sensor_index": "1.18", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 M01 VDDQ PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.18", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.19", "sensor_index": "1.19", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 M01 VDDQ PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.19", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.2", "sensor_index": "1.2", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 FIVR PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.2", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.20", "sensor_index": "1.20", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 M01 VTT PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.20", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.21", "sensor_index": "1.21", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 M23 VTT PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.21", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.22", "sensor_index": "1.22", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 M01 VPP PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.22", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.23", "sensor_index": "1.23", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 M01 VPP PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.23", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.24", "sensor_index": "1.24", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 1.5V PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.24", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.25", "sensor_index": "1.25", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 M01 VTT PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.25", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.26", "sensor_index": "1.26", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 M23 VDDQ PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.26", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.27", "sensor_index": "1.27", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 M23 VTT PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.27", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.28", "sensor_index": "1.28", "sensor_type": "voltageProbeStatus", "sensor_descr": "PS1 Voltage 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.28", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.29", "sensor_index": "1.29", "sensor_type": "voltageProbeStatus", "sensor_descr": "PS2 Voltage 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.29", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.3", "sensor_index": "1.3", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 VCORE PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.3", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.4", "sensor_index": "1.4", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 FIVR PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.4", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.5", "sensor_index": "1.5", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 3.3V PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.5", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.6", "sensor_index": "1.6", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 1.5V AUX PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.6", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.7", "sensor_index": "1.7", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 5V AUX PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.7", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.8", "sensor_index": "1.8", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 M23 VPP PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.8", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.9", "sensor_index": "1.9", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 M23 VPP PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.9", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.1", "sensor_index": "1.1", "sensor_type": "drac", "sensor_descr": "System Board Inlet Temp", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 22, "sensor_limit": 47, "sensor_limit_warn": 42, "sensor_limit_low": -7, "sensor_limit_low_warn": 3, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.2", "sensor_index": "1.2", "sensor_type": "drac", "sensor_descr": "CPU1 Temp", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 62, "sensor_limit": 85, "sensor_limit_warn": 80, "sensor_limit_low": 3, "sensor_limit_low_warn": 8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.3", "sensor_index": "1.3", "sensor_type": "drac", "sensor_descr": "CPU2 Temp", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 60, "sensor_limit": 85, "sensor_limit_warn": 80, "sensor_limit_low": 3, "sensor_limit_low_warn": 8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.4", "sensor_index": "1.4", "sensor_type": "drac", "sensor_descr": "CPU2 Temp", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 84, "sensor_limit_warn": 79, "sensor_limit_low": 3, "sensor_limit_low_warn": 8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.0", "sensor_index": "drsChassisFrontPanelAmbientTemperature.0", "sensor_type": "drac", "sensor_descr": "Chassis  Front Panel Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 18, "sensor_limit": 38, "sensor_limit_warn": null, "sensor_limit_low": 8, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.0", "sensor_index": "drsCMCAmbientTemperature.0", "sensor_type": "drac", "sensor_descr": "CMC Ambient Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.0", "sensor_index": "drsCMCProcessorTemperature.0", "sensor_type": "drac", "sensor_descr": "CMC Processor Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 48, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.*********.1", "sensor_index": "1", "sensor_type": "drac", "sensor_descr": "PS1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 230, "sensor_limit": 264, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1.1", "sensor_index": "1.1", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 243.5, "sensor_limit": 280.025, "sensor_limit_warn": null, "sensor_limit_low": 206.975, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1.2", "sensor_index": "1.2", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 234.25, "sensor_limit": 269.3875, "sensor_limit_warn": null, "sensor_limit_low": 199.1125, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1.3", "sensor_index": "1.3", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 235, "sensor_limit": 270.25, "sensor_limit_warn": null, "sensor_limit_low": 199.75, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1.4", "sensor_index": "1.4", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 228, "sensor_limit": 262.2, "sensor_limit_warn": null, "sensor_limit_low": 193.8, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1.5", "sensor_index": "1.5", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 228.5, "sensor_limit": 262.775, "sensor_limit_warn": null, "sensor_limit_low": 194.225, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1.6", "sensor_index": "1.6", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 0, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.*********.2", "sensor_index": "2", "sensor_type": "drac", "sensor_descr": "PS2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 230, "sensor_limit": 264, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "amperageProbeStatus", "state_descr": "other", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "amperageProbeStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "amperageProbeStatus", "state_descr": "ok", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "amperageProbeStatus", "state_descr": "nonCriticalUpper", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "amperageProbeStatus", "state_descr": "criticalUpper", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "amperageProbeStatus", "state_descr": "nonRecoverableUpper", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "amperageProbeStatus", "state_descr": "nonCriticalLower", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "amperageProbeStatus", "state_descr": "criticalLower", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 2}, {"state_name": "amperageProbeStatus", "state_descr": "nonRecoverableLower", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "amperageProbeStatus", "state_descr": "failed", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::controllerTable", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "DELL-RAC-MIB::controllerTable", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "DELL-RAC-MIB::controllerTable", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "DELL-RAC-MIB::controllerTable", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "DELL-RAC-MIB::controllerTable", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::controllerTable", "state_descr": "nonrecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "ready", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "online", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "foreign", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "offline", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "blocked", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "failed", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "nonraid", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 0}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "nonrecoverable", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "readonly", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 0}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drsCMCServerTable", "state_descr": "absent", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "drsCMCServerTable", "state_descr": "none", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "drsCMCServerTable", "state_descr": "basic", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drsCMCServerTable", "state_descr": "off l", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drsGlobalSystemStatus", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drsGlobalSystemStatus", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drsGlobalSystemStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drsGlobalSystemStatus", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drsGlobalSystemStatus", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drsGlobalSystemStatus", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drsKVMCurrStatus", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drsKVMCurrStatus", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drsKVMCurrStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drsKVMCurrStatus", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drsKVMCurrStatus", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drsKVMCurrStatus", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drsRedCurrStatus", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drsRedCurrStatus", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drsRedCurrStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drsRedCurrStatus", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drsRedCurrStatus", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drsRedCurrStatus", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "intrusionReading", "state_descr": "chassisNotBreached", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "intrusionReading", "state_descr": "chassisBreached", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "intrusionReading", "state_descr": "chassisBreachedPrior", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "intrusionReading", "state_descr": "chassisBreachSensorFailure", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "intrusionStatus", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "intrusionStatus", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "intrusionStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "intrusionStatus", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "intrusionStatus", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "intrusionStatus", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "memoryDeviceStatus", "state_descr": "other", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "memoryDeviceStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "memoryDeviceStatus", "state_descr": "ok", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "memoryDeviceStatus", "state_descr": "nonCritical", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "memoryDeviceStatus", "state_descr": "critical", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "memoryDeviceStatus", "state_descr": "nonRecoverable", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "processorDeviceStatus", "state_descr": "other", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "processorDeviceStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "processorDeviceStatus", "state_descr": "ok", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "processorDeviceStatus", "state_descr": "nonCritical", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "processorDeviceStatus", "state_descr": "critical", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "processorDeviceStatus", "state_descr": "nonRecoverable", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "systemBatteryStatus", "state_descr": "other", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "systemBatteryStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "systemBatteryStatus", "state_descr": "ok", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "systemBatteryStatus", "state_descr": "nonCriticalUpper", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "systemBatteryStatus", "state_descr": "criticalUpper", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "systemBatteryStatus", "state_descr": "nonRecoverableUpper", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "systemBatteryStatus", "state_descr": "nonCriticalLower", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "systemBatteryStatus", "state_descr": "criticalLower", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 2}, {"state_name": "systemBatteryStatus", "state_descr": "nonRecoverableLower", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "systemBatteryStatus", "state_descr": "failed", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 2}, {"state_name": "systemStateIDSDMCardDeviceStatusCombined", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "systemStateIDSDMCardDeviceStatusCombined", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "systemStateIDSDMCardDeviceStatusCombined", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "systemStateIDSDMCardDeviceStatusCombined", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "systemStateIDSDMCardDeviceStatusCombined", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "systemStateIDSDMCardDeviceStatusCombined", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "systemStateIDSDMCardUnitStatusCombined", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "systemStateIDSDMCardUnitStatusCombined", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "systemStateIDSDMCardUnitStatusCombined", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "systemStateIDSDMCardUnitStatusCombined", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "systemStateIDSDMCardUnitStatusCombined", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "systemStateIDSDMCardUnitStatusCombined", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "virtualDiskState", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "virtualDiskState", "state_descr": "online", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "virtualDiskState", "state_descr": "failed", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "virtualDiskState", "state_descr": "degraded", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "voltageProbeStatus", "state_descr": "other", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "voltageProbeStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "voltageProbeStatus", "state_descr": "ok", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "voltageProbeStatus", "state_descr": "nonCriticalUpper", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "voltageProbeStatus", "state_descr": "criticalUpper", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "voltageProbeStatus", "state_descr": "nonRecoverableUpper", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "voltageProbeStatus", "state_descr": "nonCriticalLower", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "voltageProbeStatus", "state_descr": "criticalLower", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 2}, {"state_name": "voltageProbeStatus", "state_descr": "nonRecoverableLower", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "voltageProbeStatus", "state_descr": "failed", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 2}]}, "poller": {"sensors": [{"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.14.1", "sensor_index": "1", "sensor_type": "drac", "sensor_descr": "Chassis 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 7.744, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.1", "sensor_index": "1.1", "sensor_type": "drac", "sensor_descr": "PS1 Current 1", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 0.8, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.2", "sensor_index": "1.2", "sensor_type": "drac", "sensor_descr": "PS2 Current 2", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.6.1.3", "sensor_index": "1.3", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2.094, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.6.1.4", "sensor_index": "1.4", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1.65, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.6.1.5", "sensor_index": "1.5", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1.523, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.6.1.6", "sensor_index": "1.6", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.1", "sensor_index": "1", "sensor_type": "drac", "sensor_descr": "System Fan1A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4440, "sensor_limit": 7992, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.10", "sensor_index": "10", "sensor_type": "drac", "sensor_descr": "System Fan5B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3720, "sensor_limit": 6696, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.11", "sensor_index": "11", "sensor_type": "drac", "sensor_descr": "System Fan6A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4560, "sensor_limit": 8208, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.12", "sensor_index": "12", "sensor_type": "drac", "sensor_descr": "System Fan6B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3480, "sensor_limit": 6264, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.2", "sensor_index": "2", "sensor_type": "drac", "sensor_descr": "System Fan1B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3240, "sensor_limit": 5832, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.3", "sensor_index": "3", "sensor_type": "drac", "sensor_descr": "System Fan2A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3720, "sensor_limit": 6696, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.4", "sensor_index": "4", "sensor_type": "drac", "sensor_descr": "System Fan2B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3360, "sensor_limit": 6048, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.5", "sensor_index": "5", "sensor_type": "drac", "sensor_descr": "System Fan3A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4560, "sensor_limit": 8208, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.6", "sensor_index": "6", "sensor_type": "drac", "sensor_descr": "System Fan3B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4080, "sensor_limit": 7344, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.7", "sensor_index": "7", "sensor_type": "drac", "sensor_descr": "System Fan4A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4560, "sensor_limit": 8208, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.8", "sensor_index": "8", "sensor_type": "drac", "sensor_descr": "System Fan4B", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4080, "sensor_limit": 7344, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "fanspeed", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.9", "sensor_index": "9", "sensor_type": "drac", "sensor_descr": "System Fan5A", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3840, "sensor_limit": 6912, "sensor_limit_warn": null, "sensor_limit_low": 720, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.3", "sensor_index": "1.3", "sensor_type": "drac", "sensor_descr": "System Board Pwr Consumption", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 168, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.3.1", "sensor_index": "drsIdlePower.1", "sensor_type": "drac", "sensor_descr": "Chassis 1 Idle Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1721, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.4.1", "sensor_index": "drsMaxPowerSpecification.1", "sensor_type": "drac", "sensor_descr": "Chassis 1 Max Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 12514, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.2.1", "sensor_index": "drsPotentialPower.1", "sensor_type": "drac", "sensor_descr": "Chassis 1 Potential Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6459, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1", "sensor_index": "drsPowerSurplus.1", "sensor_type": "drac", "sensor_descr": "Chassis 1 Surplus Power", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 6055, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.8.1", "sensor_index": "drsWattsPeakUsage.1", "sensor_type": "drac", "sensor_descr": "Chassis 1 Peak Usage", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 4803, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.13.1", "sensor_index": "drsWattsReading.1", "sensor_type": "drac", "sensor_descr": "Chassis 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1721, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.1", "sensor_index": "1.1", "sensor_type": "amperageProbeStatus", "sensor_descr": "PS1 Current 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.1", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "amperageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.2", "sensor_index": "1.2", "sensor_type": "amperageProbeStatus", "sensor_descr": "PS2 Current 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.2", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "amperageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.3", "sensor_index": "1.3", "sensor_type": "amperageProbeStatus", "sensor_descr": "System Board Pwr Consumption", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.3", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "amperageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.**********.0", "sensor_index": "controllerComponentStatus.0", "sensor_type": "DELL-RAC-MIB::controllerTable", "sensor_descr": "Shared PERC8 (Embedded) Chassis Integrated RAID 2  State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::controllerTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.**********.2", "sensor_index": "controllerComponentStatus.2", "sensor_type": "DELL-RAC-MIB::controllerTable", "sensor_descr": "Shared PERC8 (Embedded) Chassis Integrated RAID 1  State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::controllerTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.0", "sensor_index": "physicalDiskStatus.0", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:4 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.1", "sensor_index": "physicalDiskStatus.1", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:9 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.10", "sensor_index": "physicalDiskStatus.10", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:13 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.11", "sensor_index": "physicalDiskStatus.11", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:17 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.12", "sensor_index": "physicalDiskStatus.12", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:16 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.13", "sensor_index": "physicalDiskStatus.13", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:18 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.14", "sensor_index": "physicalDiskStatus.14", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:15 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.15", "sensor_index": "physicalDiskStatus.15", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:19 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.16", "sensor_index": "physicalDiskStatus.16", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:20 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.17", "sensor_index": "physicalDiskStatus.17", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:24 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.18", "sensor_index": "physicalDiskStatus.18", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:21 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.19", "sensor_index": "physicalDiskStatus.19", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:2 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.2", "sensor_index": "physicalDiskStatus.2", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:6 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.20", "sensor_index": "physicalDiskStatus.20", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:1 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.21", "sensor_index": "physicalDiskStatus.21", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:3 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.22", "sensor_index": "physicalDiskStatus.22", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:0 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.23", "sensor_index": "physicalDiskStatus.23", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:23 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.24", "sensor_index": "physicalDiskStatus.24", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:22 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.3", "sensor_index": "physicalDiskStatus.3", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:7 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.4", "sensor_index": "physicalDiskStatus.4", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:8 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.5", "sensor_index": "physicalDiskStatus.5", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:5 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.6", "sensor_index": "physicalDiskStatus.6", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:10 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.7", "sensor_index": "physicalDiskStatus.7", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:14 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.8", "sensor_index": "physicalDiskStatus.8", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:11 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.9", "sensor_index": "physicalDiskStatus.9", "sensor_type": "DELL-RAC-MIB::physicalDiskTable", "sensor_descr": "Physical Disk 0:0:12 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "DELL-RAC-MIB::physicalDiskTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsBladeCurrStatus.0", "sensor_type": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "Blade Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsCMCCurrStatus.0", "sensor_type": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "CMC Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.2.1", "sensor_index": "drsServerMonitoringCapable.1", "sensor_type": "drsCMCServerTable", "sensor_descr": "Slot 1 SLOT-01 PowerEdge M640 (VRTX) State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsCMCServerTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.2.3", "sensor_index": "drsServerMonitoringCapable.3", "sensor_type": "drsCMCServerTable", "sensor_descr": "Slot 3 SLOT-03 N/A State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsCMCServerTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.2.5", "sensor_index": "drsServerMonitoringCapable.5", "sensor_type": "drsCMCServerTable", "sensor_descr": "Slot 2 SLOT-02 PowerEdge M640 (VRTX) State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsCMCServerTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.2.7", "sensor_index": "drsServerMonitoringCapable.7", "sensor_type": "drsCMCServerTable", "sensor_descr": "Slot 4 SLOT-04 N/A State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsCMCServerTable"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsFanCurrStatus.0", "sensor_type": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "FAN Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******", "sensor_index": "drsGlobalSystemStatus.0", "sensor_type": "drsGlobalSystemStatus", "sensor_descr": "Global System Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsGlobalSystemStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsIOMCurrStatus.0", "sensor_type": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sensor_descr": "IOM Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsKVMCurrStatus.0", "sensor_type": "drsKVMCurrStatus", "sensor_descr": "KVM Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsKVMCurrStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsPowerCurrStatus.0", "sensor_type": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "sensor_descr": "Power Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsRedCurrStatus.0", "sensor_type": "drsRedCurrStatus", "sensor_descr": "Redundancy Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsRedCurrStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.0", "sensor_index": "drsTempCurrStatus.0", "sensor_type": "drsTemp<PERSON>urr<PERSON><PERSON>us", "sensor_descr": "Temperature Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.300.********.1", "sensor_index": "intrusionReading.1.1", "sensor_type": "intrusionReading", "sensor_descr": "Intrusion Reading", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "intrusionReading"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.300.********.1", "sensor_index": "intrusionStatus.1.1", "sensor_type": "intrusionStatus", "sensor_descr": "Intrusion Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "intrusionStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.1", "sensor_index": "1.1", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.A1, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.1", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.2", "sensor_index": "1.2", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.A2, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.2", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.3", "sensor_index": "1.3", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.A3, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.3", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.4", "sensor_index": "1.4", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.A4, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.4", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.5", "sensor_index": "1.5", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.B1, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.5", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.6", "sensor_index": "1.6", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.B2, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.6", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.7", "sensor_index": "1.7", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.B3, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.7", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.8", "sensor_index": "1.8", "sensor_type": "memoryDeviceStatus", "sensor_descr": "DIMM.Socket.B4, 16384 MB", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.8", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "memoryDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.1", "sensor_index": "1.1", "sensor_type": "processorDeviceStatus", "sensor_descr": "Intel(R) Xeon(R) CPU E5-2620 v3 @ 2.40GHz", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.1", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "processorDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.1100.********.2", "sensor_index": "1.2", "sensor_type": "processorDeviceStatus", "sensor_descr": "Intel(R) Xeon(R) CPU E5-2620 v3 @ 2.40GHz", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.2", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "processorDeviceStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.1", "sensor_index": "1.1", "sensor_type": "systemBatteryStatus", "sensor_descr": "System Board CMOS Battery", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.1", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "systemBatteryStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.2", "sensor_index": "1.2", "sensor_type": "systemBatteryStatus", "sensor_descr": "PERC1 ROMB Battery", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.2", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "systemBatteryStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.**********.1.60.1", "sensor_index": "systemStateIDSDMCardDeviceStatusCombined.1", "sensor_type": "systemStateIDSDMCardDeviceStatusCombined", "sensor_descr": "IDSDM Card Device Combined Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "systemStateIDSDMCardDeviceStatusCombined"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.**********.1.58.1", "sensor_index": "systemStateIDSDMCardUnitStatusCombined.1", "sensor_type": "systemStateIDSDMCardUnitStatusCombined", "sensor_descr": "IDSDM Card Unit Combined Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "systemStateIDSDMCardUnitStatusCombined"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.*********.1", "sensor_index": "1", "sensor_type": "virtualDiskState", "sensor_descr": "Virtual Disk 0", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "virtualDiskState"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.1", "sensor_index": "1.1", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 VCORE PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.1", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.10", "sensor_index": "1.10", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 1.05V PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.10", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.11", "sensor_index": "1.11", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 2.5V AUX PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.11", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.12", "sensor_index": "1.12", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 5V SWITCH PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.12", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.13", "sensor_index": "1.13", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board BP1 5V PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.13", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.14", "sensor_index": "1.14", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board PS1 PG Fail", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.14", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.15", "sensor_index": "1.15", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board PS2 PG Fail", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.15", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.16", "sensor_index": "1.16", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 DIMM PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.16", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.17", "sensor_index": "1.17", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 VCCIO PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.17", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.18", "sensor_index": "1.18", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 M01 VDDQ PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.18", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.19", "sensor_index": "1.19", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 M01 VDDQ PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.19", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.2", "sensor_index": "1.2", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 FIVR PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.2", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.20", "sensor_index": "1.20", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 M01 VTT PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.20", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.21", "sensor_index": "1.21", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 M23 VTT PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.21", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.22", "sensor_index": "1.22", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 M01 VPP PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.22", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.23", "sensor_index": "1.23", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 M01 VPP PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.23", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.24", "sensor_index": "1.24", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 1.5V PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.24", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.25", "sensor_index": "1.25", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 M01 VTT PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.25", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.26", "sensor_index": "1.26", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 M23 VDDQ PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.26", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.27", "sensor_index": "1.27", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 M23 VTT PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.27", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.28", "sensor_index": "1.28", "sensor_type": "voltageProbeStatus", "sensor_descr": "PS1 Voltage 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.28", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.29", "sensor_index": "1.29", "sensor_type": "voltageProbeStatus", "sensor_descr": "PS2 Voltage 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.29", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.3", "sensor_index": "1.3", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 VCORE PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.3", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.4", "sensor_index": "1.4", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 FIVR PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.4", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.5", "sensor_index": "1.5", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 3.3V PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.5", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.6", "sensor_index": "1.6", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 1.5V AUX PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.6", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.7", "sensor_index": "1.7", "sensor_type": "voltageProbeStatus", "sensor_descr": "System Board 5V AUX PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.7", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.8", "sensor_index": "1.8", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU2 M23 VPP PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.8", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.********.9", "sensor_index": "1.9", "sensor_type": "voltageProbeStatus", "sensor_descr": "CPU1 M23 VPP PG", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 3, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": "1.9", "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "voltageProbeStatus"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.1", "sensor_index": "1.1", "sensor_type": "drac", "sensor_descr": "System Board Inlet Temp", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 22, "sensor_limit": 47, "sensor_limit_warn": 42, "sensor_limit_low": -7, "sensor_limit_low_warn": 3, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.2", "sensor_index": "1.2", "sensor_type": "drac", "sensor_descr": "CPU1 Temp", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 62, "sensor_limit": 85, "sensor_limit_warn": 80, "sensor_limit_low": 3, "sensor_limit_low_warn": 8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.3", "sensor_index": "1.3", "sensor_type": "drac", "sensor_descr": "CPU2 Temp", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 60, "sensor_limit": 85, "sensor_limit_warn": 80, "sensor_limit_low": 3, "sensor_limit_low_warn": 8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.700.********.4", "sensor_index": "1.4", "sensor_type": "drac", "sensor_descr": "CPU2 Temp", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 39, "sensor_limit": 84, "sensor_limit_warn": 79, "sensor_limit_low": 3, "sensor_limit_low_warn": 8, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.0", "sensor_index": "drsChassisFrontPanelAmbientTemperature.0", "sensor_type": "drac", "sensor_descr": "Chassis  Front Panel Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 18, "sensor_limit": 38, "sensor_limit_warn": null, "sensor_limit_low": 8, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.0", "sensor_index": "drsCMCAmbientTemperature.0", "sensor_type": "drac", "sensor_descr": "CMC Ambient Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 51, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.********.0", "sensor_index": "drsCMCProcessorTemperature.0", "sensor_type": "drac", "sensor_descr": "CMC Processor Temperature", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 48, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.*********.1", "sensor_index": "1", "sensor_type": "drac", "sensor_descr": "PS1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 230, "sensor_limit": 264, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1.1", "sensor_index": "1.1", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 243.5, "sensor_limit": 280.025, "sensor_limit_warn": null, "sensor_limit_low": 206.975, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1.2", "sensor_index": "1.2", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 234.25, "sensor_limit": 269.3875, "sensor_limit_warn": null, "sensor_limit_low": 199.1125, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1.3", "sensor_index": "1.3", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 235, "sensor_limit": 270.25, "sensor_limit_warn": null, "sensor_limit_low": 199.75, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1.4", "sensor_index": "1.4", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 228, "sensor_limit": 262.2, "sensor_limit_warn": null, "sensor_limit_low": 193.8, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1.5", "sensor_index": "1.5", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 228.5, "sensor_limit": 262.775, "sensor_limit_warn": null, "sensor_limit_low": 194.225, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.*******.5.1.6", "sensor_index": "1.6", "sensor_type": "drac", "sensor_descr": "Chassis 1 PS-6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 0, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.674.10892.5.4.600.*********.2", "sensor_index": "2", "sensor_type": "drac", "sensor_descr": "PS2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 230, "sensor_limit": 264, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "amperageProbeStatus", "state_descr": "other", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "amperageProbeStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "amperageProbeStatus", "state_descr": "ok", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "amperageProbeStatus", "state_descr": "nonCriticalUpper", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "amperageProbeStatus", "state_descr": "criticalUpper", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "amperageProbeStatus", "state_descr": "nonRecoverableUpper", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "amperageProbeStatus", "state_descr": "nonCriticalLower", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "amperageProbeStatus", "state_descr": "criticalLower", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 2}, {"state_name": "amperageProbeStatus", "state_descr": "nonRecoverableLower", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "amperageProbeStatus", "state_descr": "failed", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::controllerTable", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "DELL-RAC-MIB::controllerTable", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "DELL-RAC-MIB::controllerTable", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "DELL-RAC-MIB::controllerTable", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "DELL-RAC-MIB::controllerTable", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::controllerTable", "state_descr": "nonrecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "ready", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "online", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "foreign", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "offline", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "blocked", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "failed", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "nonraid", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 0}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "nonrecoverable", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 2}, {"state_name": "DELL-RAC-MIB::physicalDiskTable", "state_descr": "readonly", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 0}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drsCMCServerTable", "state_descr": "absent", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "drsCMCServerTable", "state_descr": "none", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "drsCMCServerTable", "state_descr": "basic", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drsCMCServerTable", "state_descr": "off l", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drsGlobalSystemStatus", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drsGlobalSystemStatus", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drsGlobalSystemStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drsGlobalSystemStatus", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drsGlobalSystemStatus", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drsGlobalSystemStatus", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drsKVMCurrStatus", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drsKVMCurrStatus", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drsKVMCurrStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drsKVMCurrStatus", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drsKVMCurrStatus", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drsKVMCurrStatus", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drs<PERSON><PERSON>er<PERSON>urr<PERSON><PERSON>us", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drsRedCurrStatus", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drsRedCurrStatus", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drsRedCurrStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drsRedCurrStatus", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drsRedCurrStatus", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drsRedCurrStatus", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "drsTemp<PERSON>urr<PERSON><PERSON>us", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "intrusionReading", "state_descr": "chassisNotBreached", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "intrusionReading", "state_descr": "chassisBreached", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "intrusionReading", "state_descr": "chassisBreachedPrior", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "intrusionReading", "state_descr": "chassisBreachSensorFailure", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "intrusionStatus", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "intrusionStatus", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "intrusionStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "intrusionStatus", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "intrusionStatus", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "intrusionStatus", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "memoryDeviceStatus", "state_descr": "other", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "memoryDeviceStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "memoryDeviceStatus", "state_descr": "ok", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "memoryDeviceStatus", "state_descr": "nonCritical", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "memoryDeviceStatus", "state_descr": "critical", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "memoryDeviceStatus", "state_descr": "nonRecoverable", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "processorDeviceStatus", "state_descr": "other", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "processorDeviceStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "processorDeviceStatus", "state_descr": "ok", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "processorDeviceStatus", "state_descr": "nonCritical", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "processorDeviceStatus", "state_descr": "critical", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "processorDeviceStatus", "state_descr": "nonRecoverable", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "systemBatteryStatus", "state_descr": "other", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "systemBatteryStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "systemBatteryStatus", "state_descr": "ok", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "systemBatteryStatus", "state_descr": "nonCriticalUpper", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "systemBatteryStatus", "state_descr": "criticalUpper", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "systemBatteryStatus", "state_descr": "nonRecoverableUpper", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "systemBatteryStatus", "state_descr": "nonCriticalLower", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "systemBatteryStatus", "state_descr": "criticalLower", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 2}, {"state_name": "systemBatteryStatus", "state_descr": "nonRecoverableLower", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "systemBatteryStatus", "state_descr": "failed", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 2}, {"state_name": "systemStateIDSDMCardDeviceStatusCombined", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "systemStateIDSDMCardDeviceStatusCombined", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "systemStateIDSDMCardDeviceStatusCombined", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "systemStateIDSDMCardDeviceStatusCombined", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "systemStateIDSDMCardDeviceStatusCombined", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "systemStateIDSDMCardDeviceStatusCombined", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "systemStateIDSDMCardUnitStatusCombined", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "systemStateIDSDMCardUnitStatusCombined", "state_descr": "unknown", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 3}, {"state_name": "systemStateIDSDMCardUnitStatusCombined", "state_descr": "ok", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 0}, {"state_name": "systemStateIDSDMCardUnitStatusCombined", "state_descr": "nonCritical", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "systemStateIDSDMCardUnitStatusCombined", "state_descr": "critical", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "systemStateIDSDMCardUnitStatusCombined", "state_descr": "nonRecoverable", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "virtualDiskState", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "virtualDiskState", "state_descr": "online", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 0}, {"state_name": "virtualDiskState", "state_descr": "failed", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 2}, {"state_name": "virtualDiskState", "state_descr": "degraded", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "voltageProbeStatus", "state_descr": "other", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 3}, {"state_name": "voltageProbeStatus", "state_descr": "unknown", "state_draw_graph": 0, "state_value": 2, "state_generic_value": 3}, {"state_name": "voltageProbeStatus", "state_descr": "ok", "state_draw_graph": 0, "state_value": 3, "state_generic_value": 0}, {"state_name": "voltageProbeStatus", "state_descr": "nonCriticalUpper", "state_draw_graph": 0, "state_value": 4, "state_generic_value": 1}, {"state_name": "voltageProbeStatus", "state_descr": "criticalUpper", "state_draw_graph": 0, "state_value": 5, "state_generic_value": 2}, {"state_name": "voltageProbeStatus", "state_descr": "nonRecoverableUpper", "state_draw_graph": 0, "state_value": 6, "state_generic_value": 2}, {"state_name": "voltageProbeStatus", "state_descr": "nonCriticalLower", "state_draw_graph": 0, "state_value": 7, "state_generic_value": 1}, {"state_name": "voltageProbeStatus", "state_descr": "criticalLower", "state_draw_graph": 0, "state_value": 8, "state_generic_value": 2}, {"state_name": "voltageProbeStatus", "state_descr": "nonRecoverableLower", "state_draw_graph": 0, "state_value": 9, "state_generic_value": 2}, {"state_name": "voltageProbeStatus", "state_descr": "failed", "state_draw_graph": 0, "state_value": 10, "state_generic_value": 2}]}}}