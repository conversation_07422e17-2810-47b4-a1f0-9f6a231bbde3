{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.15117.1", "sysDescr": "West Mountain Radio RIGrunner 4005i", "sysContact": "<private>", "version": "4.08", "hardware": "RIGRunner 4005i", "features": null, "location": "<private>", "os": "westmountainradio", "type": "power", "serial": null, "icon": "westmountainradio.svg"}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.15117.*******", "sensor_index": "1", "sensor_type": "westmountainradio", "sensor_descr": "Outlet 1", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 0.22, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.15117.*******", "sensor_index": "2", "sensor_type": "westmountainradio", "sensor_descr": "Outlet 2", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 0.681, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.15117.*******", "sensor_index": "3", "sensor_type": "westmountainradio", "sensor_descr": "Outlet 3", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.15117.*******", "sensor_index": "4", "sensor_type": "westmountainradio", "sensor_descr": "Outlet 4", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "current", "poller_type": "snmp", "sensor_oid": ".*******.4.1.15117.*******", "sensor_index": "5", "sensor_type": "westmountainradio", "sensor_descr": "Outlet 5", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.15117.********", "sensor_index": "0", "sensor_type": "RMCU::digout1val", "sensor_descr": "Outlet 1 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "RMCU::digout1val"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.15117.********", "sensor_index": "0", "sensor_type": "RMCU::digout2val", "sensor_descr": "Outlet 2 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "RMCU::digout2val"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.15117.********", "sensor_index": "0", "sensor_type": "RMCU::digout3val", "sensor_descr": "Outlet 3 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "RMCU::digout3val"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.15117.********", "sensor_index": "0", "sensor_type": "RMCU::digout4val", "sensor_descr": "Outlet 4 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "RMCU::digout4val"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.15117.********", "sensor_index": "0", "sensor_type": "RMCU::digout5val", "sensor_descr": "Outlet 5 State", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "RMCU::digout5val"}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.15117.*******", "sensor_index": "0", "sensor_type": "westmountainradio", "sensor_descr": "Input", "group": null, "sensor_divisor": 1000, "sensor_multiplier": 1, "sensor_current": 12.033, "sensor_limit": 15.5, "sensor_limit_warn": null, "sensor_limit_low": 8, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "RMCU::digout1val", "state_descr": "outletOff", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "RMCU::digout1val", "state_descr": "outletOn", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "RMCU::digout2val", "state_descr": "outletOff", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "RMCU::digout2val", "state_descr": "outletOn", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "RMCU::digout3val", "state_descr": "outletOff", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "RMCU::digout3val", "state_descr": "outletOn", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "RMCU::digout4val", "state_descr": "outletOff", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "RMCU::digout4val", "state_descr": "outletOn", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}, {"state_name": "RMCU::digout5val", "state_descr": "outletOff", "state_draw_graph": 0, "state_value": 0, "state_generic_value": 3}, {"state_name": "RMCU::digout5val", "state_descr": "outletOn", "state_draw_graph": 0, "state_value": 1, "state_generic_value": 0}]}, "poller": "matches discovery"}}