{"os": {"discovery": {"devices": [{"sysName": null, "sysObjectID": ".*******.4.1.232.9.4.12", "sysDescr": "Integrated Lights-Out 6 1.54 Nov 15 2023", "sysContact": "<private>", "version": "1.54", "hardware": "ProLiant DL380 Gen11", "features": null, "location": null, "os": "hpe-ilo", "type": "appliance", "serial": "2MZZZZZZZZZZ", "icon": "hpe.svg"}]}, "poller": "matches discovery"}, "ports": {"discovery": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Broadcom P210tep NetXtreme-E Dual-port 10GBASE-T Ethernet PCIe Adapter - NIC", "ifName": "Broadcom P210tep NetXtreme-E Dual-port 10GBASE-T Ethernet PCIe Adapter - NIC", "portName": null, "ifIndex": 1, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Broadcom P210tep NetXtreme-E Dual-port 10GBASE-T Ethernet PCIe Adapter - NIC", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Broadcom P210tep NetXtreme-E Dual-port 10GBASE-T Ethernet PCIe Adapter - NIC", "ifName": "Broadcom P210tep NetXtreme-E Dual-port 10GBASE-T Ethernet PCIe Adapter - NIC", "portName": null, "ifIndex": 2, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Broadcom P210tep NetXtreme-E Dual-port 10GBASE-T Ethernet PCIe Adapter - NIC", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Broadcom P210p NetXtreme-E Dual-port 10Gb Ethernet PCIe Adapter", "ifName": "Broadcom P210p NetXtreme-E Dual-port 10Gb Ethernet PCIe Adapter", "portName": null, "ifIndex": 3, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Broadcom P210p NetXtreme-E Dual-port 10Gb Ethernet PCIe Adapter", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Broadcom P210p NetXtreme-E Dual-port 10Gb Ethernet PCIe Adapter", "ifName": "Broadcom P210p NetXtreme-E Dual-port 10Gb Ethernet PCIe Adapter", "portName": null, "ifIndex": 4, "ifSpeed": null, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": null, "ifAdminStatus": null, "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": null, "ifType": "ethernetCsmacd", "ifAlias": "Broadcom P210p NetXtreme-E Dual-port 10Gb Ethernet PCIe Adapter", "ifPhysAddress": null, "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": null, "ifInUcastPkts_prev": null, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": null, "ifOutUcastPkts_prev": null, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": null, "ifInErrors_prev": null, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": null, "ifOutErrors_prev": null, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": null, "ifInOctets_prev": null, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": null, "ifOutOctets_prev": null, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": null, "ifInNUcastPkts_prev": null, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": null, "ifOutNUcastPkts_prev": null, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": null, "ifInDiscards_prev": null, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": null, "ifOutDiscards_prev": null, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": null, "ifInUnknownProtos_prev": null, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": null, "ifInBroadcastPkts_prev": null, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": null, "ifOutBroadcastPkts_prev": null, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": null, "ifInMulticastPkts_prev": null, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": null, "ifOutMulticastPkts_prev": null, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}, "poller": {"ports": [{"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Broadcom P210tep NetXtreme-E Dual-port 10GBASE-T Ethernet PCIe Adapter - NIC", "ifName": "Broadcom P210tep NetXtreme-E Dual-port 10GBASE-T Ethernet PCIe Adapter - NIC", "portName": null, "ifIndex": 1, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "Broadcom P210tep NetXtreme-E Dual-port 10GBASE-T Ethernet PCIe Adapter - NIC", "ifPhysAddress": "1423f2e42190", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Broadcom P210tep NetXtreme-E Dual-port 10GBASE-T Ethernet PCIe Adapter - NIC", "ifName": "Broadcom P210tep NetXtreme-E Dual-port 10GBASE-T Ethernet PCIe Adapter - NIC", "portName": null, "ifIndex": 2, "ifSpeed": 0, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "down", "ifOperStatus_prev": "down", "ifAdminStatus": "down", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "Broadcom P210tep NetXtreme-E Dual-port 10GBASE-T Ethernet PCIe Adapter - NIC", "ifPhysAddress": "1423f2e42191", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 0, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 0, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 0, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 0, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Broadcom P210p NetXtreme-E Dual-port 10Gb Ethernet PCIe Adapter", "ifName": "Broadcom P210p NetXtreme-E Dual-port 10Gb Ethernet PCIe Adapter", "portName": null, "ifIndex": 3, "ifSpeed": 4294967295, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "Broadcom P210p NetXtreme-E Dual-port 10Gb Ethernet PCIe Adapter", "ifPhysAddress": "1423f2d828a0", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 85612, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 117095730, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 3018266594, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 85612, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}, {"port_descr_type": null, "port_descr_descr": null, "port_descr_circuit": null, "port_descr_speed": null, "port_descr_notes": null, "ifDescr": "Broadcom P210p NetXtreme-E Dual-port 10Gb Ethernet PCIe Adapter", "ifName": "Broadcom P210p NetXtreme-E Dual-port 10Gb Ethernet PCIe Adapter", "portName": null, "ifIndex": 4, "ifSpeed": 4294967295, "ifSpeed_prev": null, "ifConnectorPresent": null, "ifOperStatus": "up", "ifOperStatus_prev": "up", "ifAdminStatus": "up", "ifAdminStatus_prev": null, "ifDuplex": null, "ifMtu": 1500, "ifType": "ethernetCsmacd", "ifAlias": "Broadcom P210p NetXtreme-E Dual-port 10Gb Ethernet PCIe Adapter", "ifPhysAddress": "1423f2d828a1", "ifLastChange": 0, "ifVlan": null, "ifTrunk": null, "ignore": 0, "disabled": 0, "deleted": 0, "pagpOperationMode": null, "pagpPortState": null, "pagpPartnerDeviceId": null, "pagpPartnerLearnMethod": null, "pagpPartnerIfIndex": null, "pagpPartnerGroupIfIndex": null, "pagpPartnerDeviceName": null, "pagpEthcOperationMode": null, "pagpDeviceId": null, "pagpGroupIfIndex": null, "ifInUcastPkts": 85769, "ifInUcastPkts_prev": 0, "ifInUcastPkts_delta": null, "ifInUcastPkts_rate": null, "ifOutUcastPkts": 0, "ifOutUcastPkts_prev": 0, "ifOutUcastPkts_delta": null, "ifOutUcastPkts_rate": null, "ifInErrors": 0, "ifInErrors_prev": 0, "ifInErrors_delta": null, "ifInErrors_rate": null, "ifOutErrors": 0, "ifOutErrors_prev": 0, "ifOutErrors_delta": null, "ifOutErrors_rate": null, "ifInOctets": 3418403887, "ifInOctets_prev": 0, "ifInOctets_delta": null, "ifInOctets_rate": null, "ifOutOctets": 10151242, "ifOutOctets_prev": 0, "ifOutOctets_delta": null, "ifOutOctets_rate": null, "poll_prev": null, "ifInNUcastPkts": 85769, "ifInNUcastPkts_prev": 0, "ifInNUcastPkts_delta": null, "ifInNUcastPkts_rate": null, "ifOutNUcastPkts": 0, "ifOutNUcastPkts_prev": 0, "ifOutNUcastPkts_delta": null, "ifOutNUcastPkts_rate": null, "ifInDiscards": 0, "ifInDiscards_prev": 0, "ifInDiscards_delta": null, "ifInDiscards_rate": null, "ifOutDiscards": 0, "ifOutDiscards_prev": 0, "ifOutDiscards_delta": null, "ifOutDiscards_rate": null, "ifInUnknownProtos": 0, "ifInUnknownProtos_prev": 0, "ifInUnknownProtos_delta": null, "ifInUnknownProtos_rate": null, "ifInBroadcastPkts": 0, "ifInBroadcastPkts_prev": 0, "ifInBroadcastPkts_delta": null, "ifInBroadcastPkts_rate": null, "ifOutBroadcastPkts": 0, "ifOutBroadcastPkts_prev": 0, "ifOutBroadcastPkts_delta": null, "ifOutBroadcastPkts_rate": null, "ifInMulticastPkts": 0, "ifInMulticastPkts_prev": 0, "ifInMulticastPkts_delta": null, "ifInMulticastPkts_rate": null, "ifOutMulticastPkts": 0, "ifOutMulticastPkts_prev": 0, "ifOutMulticastPkts_delta": null, "ifOutMulticastPkts_rate": null}]}}, "mempools": {"discovery": {"mempools": [{"mempool_index": "0", "entPhysicalIndex": null, "mempool_type": "hpe-ilo", "mempool_class": "system", "mempool_precision": 1048576, "mempool_descr": "Physical Memory", "mempool_perc": 5, "mempool_perc_oid": null, "mempool_used": 28938600448, "mempool_used_oid": null, "mempool_free": 520562409472, "mempool_free_oid": ".*******.**********.2.13.2.0", "mempool_total": 549501009920, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 90}, {"mempool_index": "1", "entPhysicalIndex": null, "mempool_type": "hpe-ilo", "mempool_class": "system", "mempool_precision": 1048576, "mempool_descr": "Paging Memory", "mempool_perc": 0, "mempool_perc_oid": null, "mempool_used": -515661365248, "mempool_used_oid": null, "mempool_free": 532378812416, "mempool_free_oid": ".*******.**********.********", "mempool_total": 16717447168, "mempool_total_oid": null, "mempool_largestfree": null, "mempool_lowestfree": null, "mempool_deleted": 0, "mempool_perc_warn": 90}]}, "poller": "matches discovery"}, "route": {"discovery": {"route": [{"context_name": "", "inetCidrRouteIfIndex": 4, "inetCidrRouteType": 1, "inetCidrRouteProto": 1, "inetCidrRouteNextHopAS": 0, "inetCidrRouteMetric1": 0, "inetCidrRouteDestType": "ipv4", "inetCidrRouteDest": "0.0.0.0", "inetCidrRouteNextHopType": "ipv4", "inetCidrRouteNextHop": "***********", "inetCidrRoutePolicy": "", "inetCidrRoutePfxLen": 0}, {"context_name": "", "inetCidrRouteIfIndex": 4, "inetCidrRouteType": 1, "inetCidrRouteProto": 1, "inetCidrRouteNextHopAS": 0, "inetCidrRouteMetric1": 0, "inetCidrRouteDestType": "ipv4", "inetCidrRouteDest": "***********", "inetCidrRouteNextHopType": "ipv4", "inetCidrRouteNextHop": "***********", "inetCidrRoutePolicy": "", "inetCidrRoutePfxLen": 24}]}}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.7.0.1", "sensor_index": "cpqHeFltTolPowerSupplyBay.0.1", "sensor_type": "hpe-ilo", "sensor_descr": "PowerSupply #", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 177, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "power", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.7.0.2", "sensor_index": "cpqHeFltTolPowerSupplyBay.0.2", "sensor_type": "hpe-ilo", "sensor_descr": "PowerSupply #", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 190, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.1", "sensor_index": "cpqHeFltTolFanCondition.0.1", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.2", "sensor_index": "cpqHeFltTolFanCondition.0.2", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.3", "sensor_index": "cpqHeFltTolFanCondition.0.3", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.4", "sensor_index": "cpqHeFltTolFanCondition.0.4", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.5", "sensor_index": "cpqHeFltTolFanCondition.0.5", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.9.0.6", "sensor_index": "cpqHeFltTolFanCondition.0.6", "sensor_type": "cpqHeFltTolFanCondition", "sensor_descr": "Fan #6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolFanCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.1", "sensor_index": "cpqHeFltTolPowerSupplyCondition.0.1", "sensor_type": "cpqHeFltTolPowerSupplyCondition", "sensor_descr": "PowerSupply #", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolPowerSupplyCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.2", "sensor_index": "cpqHeFltTolPowerSupplyCondition.0.2", "sensor_type": "cpqHeFltTolPowerSupplyCondition", "sensor_descr": "PowerSupply #", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeFltTolPowerSupplyCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.0", "sensor_index": "cpqHeResMem2ModuleCondition.0", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.1", "sensor_index": "cpqHeResMem2ModuleCondition.1", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.10", "sensor_index": "cpqHeResMem2ModuleCondition.10", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.11", "sensor_index": "cpqHeResMem2ModuleCondition.11", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.12", "sensor_index": "cpqHeResMem2ModuleCondition.12", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 13", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.13", "sensor_index": "cpqHeResMem2ModuleCondition.13", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 14", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.14", "sensor_index": "cpqHeResMem2ModuleCondition.14", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 15", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.15", "sensor_index": "cpqHeResMem2ModuleCondition.15", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 16", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.16", "sensor_index": "cpqHeResMem2ModuleCondition.16", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.17", "sensor_index": "cpqHeResMem2ModuleCondition.17", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.18", "sensor_index": "cpqHeResMem2ModuleCondition.18", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.19", "sensor_index": "cpqHeResMem2ModuleCondition.19", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.2", "sensor_index": "cpqHeResMem2ModuleCondition.2", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 3", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.20", "sensor_index": "cpqHeResMem2ModuleCondition.20", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.21", "sensor_index": "cpqHeResMem2ModuleCondition.21", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.22", "sensor_index": "cpqHeResMem2ModuleCondition.22", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.23", "sensor_index": "cpqHeResMem2ModuleCondition.23", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.24", "sensor_index": "cpqHeResMem2ModuleCondition.24", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.25", "sensor_index": "cpqHeResMem2ModuleCondition.25", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.26", "sensor_index": "cpqHeResMem2ModuleCondition.26", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 11", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.27", "sensor_index": "cpqHeResMem2ModuleCondition.27", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 12", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.28", "sensor_index": "cpqHeResMem2ModuleCondition.28", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 13", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.29", "sensor_index": "cpqHeResMem2ModuleCondition.29", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 14", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.3", "sensor_index": "cpqHeResMem2ModuleCondition.3", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 4", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.30", "sensor_index": "cpqHeResMem2ModuleCondition.30", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 15", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.31", "sensor_index": "cpqHeResMem2ModuleCondition.31", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 2 DIMM 16", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.4", "sensor_index": "cpqHeResMem2ModuleCondition.4", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 5", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.5", "sensor_index": "cpqHeResMem2ModuleCondition.5", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 6", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.6", "sensor_index": "cpqHeResMem2ModuleCondition.6", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 7", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.7", "sensor_index": "cpqHeResMem2ModuleCondition.7", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 8", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.8", "sensor_index": "cpqHeResMem2ModuleCondition.8", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 9", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*********.20.9", "sensor_index": "cpqHeResMem2ModuleCondition.9", "sensor_type": "cpqHeResMem2ModuleCondition", "sensor_descr": "PROC 1 DIMM 10", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeResMem2ModuleCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.********.4.0.1", "sensor_index": "cpqHeSysBatteryCondition.0.1", "sensor_type": "cpqHeSysBatteryCondition", "sensor_descr": "Battery Condition (96W 875241-B21)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeSysBatteryCondition"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.********.5.0.1", "sensor_index": "cpqHeSysBatteryStatus.0.1", "sensor_type": "cpqHeSysBatteryStatus", "sensor_descr": "Battery Status (96W 875241-B21)", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqHeSysBatteryStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.6.0", "sensor_index": "cpqSeCpuStatus.0", "sensor_type": "cpqSeCpuStatus", "sensor_descr": "Processor #1", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqSeCpuStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.6.1", "sensor_index": "cpqSeCpuStatus.1", "sensor_type": "cpqSeCpuStatus", "sensor_descr": "Processor #2", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "cpqSeCpuStatus"}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.1", "sensor_index": "cpqHeTemperatureCelsius.0.1", "sensor_type": "hpe-ilo", "sensor_descr": "ambient", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 14, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 4, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.10", "sensor_index": "cpqHeTemperatureCelsius.0.10", "sensor_type": "hpe-ilo", "sensor_descr": "memory", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 31, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 21, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.12", "sensor_index": "cpqHeTemperatureCelsius.0.12", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 50, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 40, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.13", "sensor_index": "cpqHeTemperatureCelsius.0.13", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 42, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 32, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.14", "sensor_index": "cpqHeTemperatureCelsius.0.14", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 46, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 36, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.18", "sensor_index": "cpqHeTemperatureCelsius.0.18", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 14, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 4, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.19", "sensor_index": "cpqHeTemperatureCelsius.0.19", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 49, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 39, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.2", "sensor_index": "cpqHeTemperatureCelsius.0.2", "sensor_type": "hpe-ilo", "sensor_descr": "cpu", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 51, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 41, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.20", "sensor_index": "cpqHeTemperatureCelsius.0.20", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 63, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 53, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.21", "sensor_index": "cpqHeTemperatureCelsius.0.21", "sensor_type": "hpe-ilo", "sensor_descr": "powerSupply", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 27, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 17, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.22", "sensor_index": "cpqHeTemperatureCelsius.0.22", "sensor_type": "hpe-ilo", "sensor_descr": "powerSupply", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.23", "sensor_index": "cpqHeTemperatureCelsius.0.23", "sensor_type": "hpe-ilo", "sensor_descr": "powerSupply", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 35, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 25, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.24", "sensor_index": "cpqHeTemperatureCelsius.0.24", "sensor_type": "hpe-ilo", "sensor_descr": "powerSupply", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 40, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 30, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.25", "sensor_index": "cpqHeTemperatureCelsius.0.25", "sensor_type": "hpe-ilo", "sensor_descr": "ioBoard", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 54, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 44, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.27", "sensor_index": "cpqHeTemperatureCelsius.0.27", "sensor_type": "hpe-ilo", "sensor_descr": "ioBoard", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 86, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 76, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.29", "sensor_index": "cpqHeTemperatureCelsius.0.29", "sensor_type": "hpe-ilo", "sensor_descr": "ioBoard", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 70, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 60, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.3", "sensor_index": "cpqHeTemperatureCelsius.0.3", "sensor_type": "hpe-ilo", "sensor_descr": "cpu", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 42, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 32, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.35", "sensor_index": "cpqHeTemperatureCelsius.0.35", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 20, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 10, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.36", "sensor_index": "cpqHeTemperatureCelsius.0.36", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 30, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 20, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.37", "sensor_index": "cpqHeTemperatureCelsius.0.37", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 34, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 24, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.38", "sensor_index": "cpqHeTemperatureCelsius.0.38", "sensor_type": "hpe-ilo", "sensor_descr": "system", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 38, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 28, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.39", "sensor_index": "cpqHeTemperatureCelsius.0.39", "sensor_type": "hpe-ilo", "sensor_descr": "powerSupply", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 35, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 25, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.4", "sensor_index": "cpqHeTemperatureCelsius.0.4", "sensor_type": "hpe-ilo", "sensor_descr": "memory", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 36, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 26, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.6", "sensor_index": "cpqHeTemperatureCelsius.0.6", "sensor_type": "hpe-ilo", "sensor_descr": "memory", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 36, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 26, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "temperature", "poller_type": "snmp", "sensor_oid": ".*******.*********.*******.4.0.8", "sensor_index": "cpqHeTemperatureCelsius.0.8", "sensor_type": "hpe-ilo", "sensor_descr": "memory", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 28, "sensor_limit": 90, "sensor_limit_warn": null, "sensor_limit_low": 18, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "cpqHeFltTolFanCondition", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqHeFltTolFanCondition", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqHeFltTolFanCondition", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "cpqHeFltTolFanCondition", "state_descr": "failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "cpqHeFltTolPowerSupplyCondition", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqHeFltTolPowerSupplyCondition", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqHeFltTolPowerSupplyCondition", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "cpqHeFltTolPowerSupplyCondition", "state_descr": "failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "cpqHeResMem2ModuleCondition", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqHeResMem2ModuleCondition", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqHeResMem2ModuleCondition", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "cpqHeResMem2ModuleCondition", "state_descr": "degradedModuleIndexUnknown", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "cpqHeSysBatteryCondition", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqHeSysBatteryCondition", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqHeSysBatteryCondition", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryCondition", "state_descr": "failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "generalFailure", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownHighResistance", "state_draw_graph": 3, "state_value": 3, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownLowVoltage", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownShortCircuit", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownChargeTimeout", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownOverTemperature", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownDischargeMinVoltage", "state_draw_graph": 1, "state_value": 8, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownDischargeCurrent", "state_draw_graph": 1, "state_value": 9, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownLoadCountHigh", "state_draw_graph": 1, "state_value": 10, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownEnablePin", "state_draw_graph": 1, "state_value": 11, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownOverCurrent", "state_draw_graph": 1, "state_value": 12, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownPermanentFailure", "state_draw_graph": 1, "state_value": 13, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "shutdownBackupTimeExceeded", "state_draw_graph": 1, "state_value": 14, "state_generic_value": 2}, {"state_name": "cpqHeSysBatteryStatus", "state_descr": "predictiveFailure", "state_draw_graph": 1, "state_value": 15, "state_generic_value": 2}, {"state_name": "cpqSeCpuStatus", "state_descr": "other", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "cpqSeCpuStatus", "state_descr": "ok", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "cpqSeCpuStatus", "state_descr": "degraded", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 1}, {"state_name": "cpqSeCpuStatus", "state_descr": "failed", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}]}, "poller": "matches discovery"}, "storage": {"discovery": [], "poller": []}}