{"os": {"discovery": {"devices": [{"sysName": "<private>", "sysObjectID": ".*******.4.1.3808", "sysDescr": "UPS Local", "sysContact": "<private>", "version": "CRDA103_261", "hardware": "CP1000PFCLCD", "features": null, "location": "<private>", "os": "cyberpower", "type": "power", "serial": null, "icon": "cyberpower.svg"}]}, "poller": "matches discovery"}, "sensors": {"discovery": {"sensors": [{"sensor_deleted": 0, "sensor_class": "charge", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3808.*******.2.1.0", "sensor_index": "upsAdvanceBatteryCapacity.0", "sensor_type": "cyberpower", "sensor_descr": "Battery Capacity", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 100, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "frequency", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3808.*******.2.2.0", "sensor_index": "upsAdvanceOutputFrequency.0", "sensor_type": "cyberpower", "sensor_descr": "Output", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 0, "sensor_limit": 0, "sensor_limit_warn": null, "sensor_limit_low": 0, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "load", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3808.*******.2.3.0", "sensor_index": "upsAdvanceOutputLoad.0", "sensor_type": "cyberpower", "sensor_descr": "Load", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 24, "sensor_limit": 80, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "runtime", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3808.*******.2.4.0", "sensor_index": "upsAdvanceBatteryRunTimeRemaining.0", "sensor_type": "cyberpower", "sensor_descr": "Battery Runtime", "group": null, "sensor_divisor": 6000, "sensor_multiplier": 1, "sensor_current": 20, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3808.*******.2.5.0", "sensor_index": "upsAdvanceBatteryReplaceIndicator.0", "sensor_type": "upsAdvanceBatteryReplaceIndicator", "sensor_descr": "Battery Replace Indicator", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "upsAdvanceBatteryReplaceIndicator"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3808.*******.2.5.0", "sensor_index": "upsAdvanceInputLineFailCause.0", "sensor_type": "upsAdvanceInputLineFailCause", "sensor_descr": "Input Line Cause", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "upsAdvanceInputLineFailCause"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3808.*******.2.6.0", "sensor_index": "upsAdvanceInputStatus.0", "sensor_type": "upsAdvanceInputStatus", "sensor_descr": "Input Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 1, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "upsAdvanceInputStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3808.*******.1.1.0", "sensor_index": "upsBaseBatteryStatus.0", "sensor_type": "upsBaseBatteryStatus", "sensor_descr": "Battery Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "upsBaseBatteryStatus"}, {"sensor_deleted": 0, "sensor_class": "state", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3808.*******.1.1.0", "sensor_index": "upsBaseOutputStatus.0", "sensor_type": "upsBaseOutputStatus", "sensor_descr": "Output Status", "group": null, "sensor_divisor": 1, "sensor_multiplier": 1, "sensor_current": 2, "sensor_limit": null, "sensor_limit_warn": null, "sensor_limit_low": null, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": "upsBaseOutputStatus"}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3808.*******.2.2.0", "sensor_index": "upsAdvanceBatteryVoltage.0", "sensor_type": "cyberpower", "sensor_descr": "Battery Voltage", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 24, "sensor_limit": 27.6, "sensor_limit_warn": null, "sensor_limit_low": 20.4, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3808.*******.2.1.0", "sensor_index": "upsAdvanceInputLineVoltage.0", "sensor_type": "cyberpower", "sensor_descr": "Input Line Voltage", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 123, "sensor_limit": 141.45, "sensor_limit_warn": null, "sensor_limit_low": 104.55, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}, {"sensor_deleted": 0, "sensor_class": "voltage", "poller_type": "snmp", "sensor_oid": ".*******.4.1.3808.*******.2.1.0", "sensor_index": "upsAdvanceOutputVoltage.0", "sensor_type": "cyberpower", "sensor_descr": "Output Voltage", "group": null, "sensor_divisor": 10, "sensor_multiplier": 1, "sensor_current": 123, "sensor_limit": 141.45, "sensor_limit_warn": null, "sensor_limit_low": 104.55, "sensor_limit_low_warn": null, "sensor_alert": 1, "sensor_custom": "No", "entPhysicalIndex": null, "entPhysicalIndex_measured": null, "sensor_prev": null, "user_func": null, "rrd_type": "GAUGE", "state_name": null}], "state_indexes": [{"state_name": "upsAdvanceBatteryReplaceIndicator", "state_descr": "No", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "upsAdvanceBatteryReplaceIndicator", "state_descr": "Replace", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "upsAdvanceInputLineFailCause", "state_descr": "No Transfer", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "upsAdvanceInputLineFailCause", "state_descr": "High Voltage", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "upsAdvanceInputLineFailCause", "state_descr": "<PERSON> Out", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "upsAdvanceInputLineFailCause", "state_descr": "Self Test", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "upsAdvanceInputStatus", "state_descr": "Normal", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 0}, {"state_name": "upsAdvanceInputStatus", "state_descr": "Over Voltage", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 2}, {"state_name": "upsAdvanceInputStatus", "state_descr": "Under Voltage", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "upsAdvanceInputStatus", "state_descr": "Frequency Failure", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 2}, {"state_name": "upsAdvanceInputStatus", "state_descr": "Blackout", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 2}, {"state_name": "upsBaseBatteryStatus", "state_descr": "Known", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "upsBaseBatteryStatus", "state_descr": "Normal", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "upsBaseBatteryStatus", "state_descr": "Low", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "upsBaseOutputStatus", "state_descr": "Unknown", "state_draw_graph": 1, "state_value": 1, "state_generic_value": 3}, {"state_name": "upsBaseOutputStatus", "state_descr": "Online", "state_draw_graph": 1, "state_value": 2, "state_generic_value": 0}, {"state_name": "upsBaseOutputStatus", "state_descr": "On Battery", "state_draw_graph": 1, "state_value": 3, "state_generic_value": 2}, {"state_name": "upsBaseOutputStatus", "state_descr": "On Boost", "state_draw_graph": 1, "state_value": 4, "state_generic_value": 1}, {"state_name": "upsBaseOutputStatus", "state_descr": "On Sleep", "state_draw_graph": 1, "state_value": 5, "state_generic_value": 1}, {"state_name": "upsBaseOutputStatus", "state_descr": "Off", "state_draw_graph": 1, "state_value": 6, "state_generic_value": 2}, {"state_name": "upsBaseOutputStatus", "state_descr": "Rebooting", "state_draw_graph": 1, "state_value": 7, "state_generic_value": 1}]}, "poller": "matches discovery"}}