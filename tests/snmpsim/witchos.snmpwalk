.1.0.8802.1.1.2.1.1.7.1.1.1.4.********** = Hex-STRING: FF C0
.1.0.8802.1.1.*******.0 = INTEGER: 4
.1.0.8802.1.1.2.1.3.2.0 = Hex-STRING: 20 97 27 22 7B A2
.1.0.8802.1.1.2.1.3.7.1.2.1 = INTEGER: 1
.1.0.8802.1.1.2.1.3.7.1.2.2 = INTEGER: 1
.1.0.8802.1.1.2.1.3.7.1.2.3 = INTEGER: 1
.1.0.8802.1.1.2.1.3.7.1.2.4 = INTEGER: 1
.1.0.8802.1.1.2.1.3.7.1.2.5 = INTEGER: 1
.1.0.8802.1.1.2.1.3.7.1.2.6 = INTEGER: 1
.1.0.8802.1.1.2.1.3.7.1.2.7 = INTEGER: 1
.1.0.8802.1.1.2.1.3.7.1.2.8 = INTEGER: 1
.1.0.8802.1.1.2.1.3.7.1.2.9 = INTEGER: 1
.1.0.8802.1.1.2.1.3.7.1.2.10 = INTEGER: 1
.1.0.8802.1.1.2.1.3.7.1.3.1 = STRING: "port1"
.1.0.8802.1.1.2.1.3.7.1.3.2 = STRING: "port2"
.1.0.8802.1.1.2.1.3.7.1.3.3 = STRING: "port3"
.1.0.8802.1.1.2.1.3.7.1.3.4 = STRING: "port4"
.1.0.8802.1.1.2.1.3.7.1.3.5 = STRING: "port5"
.1.0.8802.1.1.2.1.3.7.1.3.6 = STRING: "port6"
.1.0.8802.1.1.2.1.3.7.1.3.7 = STRING: "port7"
.1.0.8802.1.1.2.1.3.7.1.3.8 = STRING: "port8"
.1.0.8802.1.1.2.1.3.7.1.3.9 = STRING: "sfp1"
.1.0.8802.1.1.2.1.3.7.1.3.10 = STRING: "sfp2"
.1.0.8802.1.1.2.1.3.7.1.4.1 = STRING: "port1"
.1.0.8802.1.1.2.1.3.7.1.4.2 = STRING: "port2"
.1.0.8802.1.1.2.1.3.7.1.4.3 = STRING: "port3"
.1.0.8802.1.1.2.1.3.7.1.4.4 = STRING: "port4"
.1.0.8802.1.1.2.1.3.7.1.4.5 = STRING: "port5"
.1.0.8802.1.1.2.1.3.7.1.4.6 = STRING: "port6"
.1.0.8802.1.1.2.1.3.7.1.4.7 = STRING: "port7"
.1.0.8802.1.1.2.1.3.7.1.4.8 = STRING: "port8"
.1.0.8802.1.1.2.1.3.7.1.4.9 = STRING: "sfp1"
.1.0.8802.1.1.2.1.3.7.1.4.10 = STRING: "sfp2"
.1.0.8802.1.1.2.1.3.*******.4.********** = INTEGER: 5
.1.0.8802.1.1.2.1.3.8.1.4.1.4.********** = INTEGER: 2
.1.0.8802.1.1.2.1.3.8.1.5.1.4.********** = INTEGER: 14
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.1.1 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.1.2 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.1.3 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.1.4 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.1.5 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.1.6 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.1.7 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.1.8 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.1.9 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.1.10 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.2.1 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.2.2 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.2.3 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.2.4 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.2.5 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.2.6 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.2.7 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.2.8 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.2.9 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.2.10 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.3.1 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.3.2 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.3.3 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.3.4 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.3.5 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.3.6 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.3.7 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.3.8 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.3.9 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.3.10 = Gauge32: 0
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.6.1 = ""
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.6.2 = ""
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.6.3 = ""
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.6.4 = ""
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.6.5 = ""
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.6.6 = ""
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.6.7 = ""
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.6.8 = ""
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.6.9 = ""
.1.0.8802.1.1.2.1.5.3791.1.2.1.1.6.10 = ""
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.1.1 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.1.2 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.1.3 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.1.4 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.1.5 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.1.6 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.1.7 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.1.8 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.1.9 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.1.10 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.2.1 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.2.2 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.2.3 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.2.4 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.2.5 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.2.6 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.2.7 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.2.8 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.2.9 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.2.10 = INTEGER: 1
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.3.1 = Hex-STRING: 80 36
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.3.2 = Hex-STRING: C0 36
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.3.3 = Hex-STRING: C0 36
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.3.4 = Hex-STRING: C0 36
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.3.5 = Hex-STRING: C0 36
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.3.6 = Hex-STRING: C0 36
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.3.7 = Hex-STRING: C0 36
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.3.8 = Hex-STRING: C0 36
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.3.9 = Hex-STRING: 00 01
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.3.10 = Hex-STRING: 00 01
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.4.1 = INTEGER: 16
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.4.2 = INTEGER: 16
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.4.3 = INTEGER: 30
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.4.4 = INTEGER: 30
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.4.5 = INTEGER: 16
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.4.6 = INTEGER: 16
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.4.7 = INTEGER: 16
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.4.8 = INTEGER: 16
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.4.9 = INTEGER: 30
.1.0.8802.1.1.2.1.5.4623.1.2.1.1.4.10 = INTEGER: 30
.1.2.840.10036.3.1.1.0 = STRING: "RTID"
.*******.2.1.1.1.0 = STRING: "TSW212 V1.0               TSW21200XXXX         6001244780           1 V  1  0  0"
.*******.2.1.1.2.0 = OID: .*******.4.1.24686
.*******.2.1.1.3.0 = Timeticks: (7426300) 20:37:43.00
.*******.2.1.1.4.0 = STRING: "<EMAIL>"
.*******.2.1.1.5.0 = STRING: "customer-location-switch"
.*******.2.1.1.6.0 = STRING: "location[72.682576,34.574544]"
.*******.2.1.1.7.0 = INTEGER: 78
.*******.*******.0 = Timeticks: (27) 0:00:00.27
.*******.*******.1.2.1 = OID: .*******.2.1.4
.*******.*******.1.2.2 = OID: .*******.2.1.49
.*******.*******.1.2.3 = OID: .*******.2.1.50
.*******.*******.1.2.4 = OID: .*******.********.2.1
.*******.*******.1.2.5 = OID: .*******.********.1.1
.*******.*******.1.2.6 = OID: .*******.********.1.1
.*******.*******.1.2.7 = OID: .*******.********.1.1
.*******.*******.1.2.8 = OID: .*******.**********
.*******.*******.1.3.1 = STRING: "The MIB module for managing IP and ICMP implementations"
.*******.*******.1.3.2 = STRING: "The MIB module for managing TCP implementations"
.*******.*******.1.3.3 = STRING: "The MIB module for managing UDP implementations"
.*******.*******.1.3.4 = STRING: "View-based Access Control Model for SNMP."
.*******.*******.1.3.5 = STRING: "The SNMP Management Architecture MIB."
.*******.*******.1.3.6 = STRING: "The MIB for Message Processing and Dispatching."
.*******.*******.1.3.7 = STRING: "The management information definitions for the SNMP User-based Security Model."
.*******.*******.1.3.8 = STRING: "RFC 2667 TUNNEL-MIB implementation for Linux 2.2.x kernels."
.*******.*******.1.4.1 = Timeticks: (12) 0:00:00.12
.*******.*******.1.4.2 = Timeticks: (12) 0:00:00.12
.*******.*******.1.4.3 = Timeticks: (13) 0:00:00.13
.*******.*******.1.4.4 = Timeticks: (13) 0:00:00.13
.*******.*******.1.4.5 = Timeticks: (13) 0:00:00.13
.*******.*******.1.4.6 = Timeticks: (13) 0:00:00.13
.*******.*******.1.4.7 = Timeticks: (13) 0:00:00.13
.*******.*******.1.4.8 = Timeticks: (27) 0:00:00.27
.*******.2.1.2.1.0 = INTEGER: 14
.*******.*******.1.1.1 = INTEGER: 1
.*******.*******.1.1.2 = INTEGER: 2
.*******.*******.1.1.3 = INTEGER: 3
.*******.*******.1.1.4 = INTEGER: 4
.*******.*******.1.1.5 = INTEGER: 5
.*******.*******.1.1.6 = INTEGER: 6
.*******.*******.1.1.7 = INTEGER: 7
.*******.2.1.2.******* = INTEGER: 8
.*******.2.1.2.******* = INTEGER: 9
.*******.*******.1.1.10 = INTEGER: 10
.*******.*******.1.1.11 = INTEGER: 11
.*******.*******.1.1.12 = INTEGER: 12
.*******.*******.1.1.13 = INTEGER: 13
.*******.*******.1.1.14 = INTEGER: 14
.*******.*******.1.2.1 = STRING: "lo"
.*******.*******.1.2.2 = STRING: "eth0"
.*******.*******.1.2.3 = STRING: "port1"
.*******.*******.1.2.4 = STRING: "port2"
.*******.*******.1.2.5 = STRING: "port3"
.*******.*******.1.2.6 = STRING: "port4"
.*******.*******.1.2.7 = STRING: "port5"
.*******.*******.1.2.8 = STRING: "port6"
.*******.*******.1.2.9 = STRING: "port7"
.*******.*******.1.2.10 = STRING: "port8"
.*******.*******.1.2.11 = STRING: "sfp1"
.*******.*******.1.2.12 = STRING: "sfp2"
.*******.*******.1.2.13 = STRING: "br0"
.*******.*******.1.2.14 = STRING: "br0.1"
.*******.*******.1.3.1 = INTEGER: 24
.*******.*******.1.3.2 = INTEGER: 6
.*******.*******.1.3.3 = INTEGER: 6
.*******.*******.1.3.4 = INTEGER: 6
.*******.*******.1.3.5 = INTEGER: 6
.*******.*******.1.3.6 = INTEGER: 6
.*******.*******.1.3.7 = INTEGER: 6
.*******.*******.1.3.8 = INTEGER: 6
.*******.*******.1.3.9 = INTEGER: 6
.*******.*******.1.3.10 = INTEGER: 6
.*******.*******.1.3.11 = INTEGER: 6
.*******.*******.1.3.12 = INTEGER: 6
.*******.*******.1.3.13 = INTEGER: 6
.*******.*******.1.3.14 = INTEGER: 6
.*******.*******.1.4.1 = INTEGER: 65536
.*******.*******.1.4.2 = INTEGER: 1504
.*******.*******.1.4.3 = INTEGER: 1500
.*******.*******.1.4.4 = INTEGER: 1500
.*******.*******.1.4.5 = INTEGER: 1500
.*******.*******.1.4.6 = INTEGER: 1500
.*******.2.1.2.******* = INTEGER: 1500
.*******.2.1.2.******* = INTEGER: 1500
.*******.2.1.2.******* = INTEGER: 1500
.*******.2.1.2.******** = INTEGER: 1500
.*******.2.1.2.******** = INTEGER: 1500
.*******.2.1.2.******** = INTEGER: 1500
.*******.2.1.2.******** = INTEGER: 1500
.*******.2.1.2.******** = INTEGER: 1500
.*******.*******.1.5.1 = Gauge32: 10000000
.*******.*******.1.5.2 = Gauge32: 1000000000
.*******.*******.1.5.3 = Gauge32: 100000000
.*******.*******.1.5.4 = Gauge32: 100000000
.*******.*******.1.5.5 = Gauge32: 1000000000
.*******.*******.1.5.6 = Gauge32: 1000000000
.*******.*******.1.5.7 = Gauge32: 0
.*******.*******.1.5.8 = Gauge32: 0
.*******.*******.1.5.9 = Gauge32: 0
.*******.*******.1.5.10 = Gauge32: 100000000
.*******.*******.1.5.11 = Gauge32: 1000000000
.*******.*******.1.5.12 = Gauge32: 1000000000
.*******.*******.1.5.13 = Gauge32: 1000000000
.*******.*******.1.5.14 = Gauge32: 1000000000
.*******.*******.1.6.1 = ""
.*******.*******.1.6.2 = Hex-STRING: 20 97 27 22 7B A2
.*******.*******.1.6.3 = Hex-STRING: 20 97 27 22 7B A3
.*******.*******.1.6.4 = Hex-STRING: 20 97 27 22 7B A4
.*******.*******.1.6.5 = Hex-STRING: 20 97 27 22 7B A5
.*******.*******.1.6.6 = Hex-STRING: 20 97 27 22 7B A6
.*******.*******.1.6.7 = Hex-STRING: 20 97 27 22 7B A7
.*******.*******.1.6.8 = Hex-STRING: 20 97 27 22 7B A8
.*******.*******.1.6.9 = Hex-STRING: 20 97 27 22 7B A9
.*******.*******.1.6.10 = Hex-STRING: 20 97 27 22 7B AA
.*******.*******.1.6.11 = Hex-STRING: 20 97 27 22 7B AB
.*******.*******.1.6.12 = Hex-STRING: 20 97 27 22 7B AC
.*******.*******.1.6.13 = Hex-STRING: 20 97 27 22 7B A2
.*******.*******.1.6.14 = Hex-STRING: 20 97 27 22 7B A2
.*******.*******.1.7.1 = INTEGER: 1
.*******.*******.1.7.2 = INTEGER: 1
.*******.*******.1.7.3 = INTEGER: 1
.*******.*******.1.7.4 = INTEGER: 1
.*******.*******.1.7.5 = INTEGER: 1
.*******.*******.1.7.6 = INTEGER: 1
.*******.*******.1.7.7 = INTEGER: 1
.*******.*******.1.7.8 = INTEGER: 1
.*******.*******.1.7.9 = INTEGER: 1
.*******.*******.1.7.10 = INTEGER: 1
.*******.*******.1.7.11 = INTEGER: 1
.*******.*******.1.7.12 = INTEGER: 1
.*******.*******.1.7.13 = INTEGER: 1
.*******.*******.1.7.14 = INTEGER: 1
.*******.*******.1.8.1 = INTEGER: 1
.*******.*******.1.8.2 = INTEGER: 1
.*******.*******.1.8.3 = INTEGER: 1
.*******.*******.1.8.4 = INTEGER: 1
.*******.*******.1.8.5 = INTEGER: 1
.*******.*******.1.8.6 = INTEGER: 1
.*******.*******.1.8.7 = INTEGER: 2
.*******.*******.1.8.8 = INTEGER: 2
.*******.*******.1.8.9 = INTEGER: 2
.*******.*******.1.8.10 = INTEGER: 1
.*******.*******.1.8.11 = INTEGER: 2
.*******.*******.1.8.12 = INTEGER: 2
.*******.*******.1.8.13 = INTEGER: 1
.*******.*******.1.8.14 = INTEGER: 1
.*******.*******.1.9.1 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.2 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.3 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.4 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.5 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.6 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.7 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.8 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.9 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.10 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.11 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.12 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.13 = Timeticks: (0) 0:00:00.00
.*******.*******.1.9.14 = Timeticks: (0) 0:00:00.00
.*******.*******.1.10.1 = Counter32: 4928
.*******.*******.1.10.2 = Counter32: 4438805
.*******.*******.1.10.3 = Counter32: 2708
.*******.*******.1.10.4 = Counter32: 3904
.*******.*******.1.10.5 = Counter32: 2932
.*******.*******.1.10.6 = Counter32: 16930
.*******.*******.1.10.7 = Counter32: 0
.*******.*******.1.10.8 = Counter32: 0
.*******.*******.1.10.9 = Counter32: 0
.*******.*******.1.10.10 = Counter32: 3523023
.*******.*******.1.10.11 = Counter32: 0
.*******.*******.1.10.12 = Counter32: 0
.*******.*******.1.10.13 = Counter32: 3170641
.*******.*******.1.10.14 = Counter32: 3170641
.*******.*******.1.11.1 = Counter32: 64
.*******.*******.1.11.2 = Counter32: 49406
.*******.*******.1.11.3 = Counter32: 49
.*******.*******.1.11.4 = Counter32: 75
.*******.*******.1.11.5 = Counter32: 38
.*******.*******.1.11.6 = Counter32: 343
.*******.*******.1.11.7 = Counter32: 0
.*******.*******.1.11.8 = Counter32: 0
.*******.*******.1.11.9 = Counter32: 0
.*******.*******.1.11.10 = Counter32: 48901
.*******.*******.1.11.11 = Counter32: 0
.*******.*******.1.11.12 = Counter32: 0
.*******.*******.1.11.13 = Counter32: 41023
.*******.*******.1.11.14 = Counter32: 41023
.*******.*******.1.12.1 = Counter32: 0
.*******.*******.1.12.2 = Counter32: 0
.*******.*******.1.12.3 = Counter32: 0
.*******.*******.1.12.4 = Counter32: 0
.*******.*******.1.12.5 = Counter32: 0
.*******.*******.1.12.6 = Counter32: 0
.*******.*******.1.12.7 = Counter32: 0
.*******.*******.1.12.8 = Counter32: 0
.*******.*******.1.12.9 = Counter32: 0
.*******.*******.1.12.10 = Counter32: 0
.*******.*******.1.12.11 = Counter32: 0
.*******.*******.1.12.12 = Counter32: 0
.*******.*******.1.12.13 = Counter32: 147
.*******.*******.1.12.14 = Counter32: 147
.*******.*******.1.13.1 = Counter32: 0
.*******.*******.1.13.2 = Counter32: 0
.*******.*******.1.13.3 = Counter32: 0
.*******.*******.1.13.4 = Counter32: 0
.*******.*******.1.13.5 = Counter32: 0
.*******.*******.1.13.6 = Counter32: 0
.*******.*******.1.13.7 = Counter32: 0
.*******.*******.1.13.8 = Counter32: 0
.*******.*******.1.13.9 = Counter32: 0
.*******.*******.1.13.10 = Counter32: 0
.*******.*******.1.13.11 = Counter32: 0
.*******.*******.1.13.12 = Counter32: 0
.*******.*******.1.13.13 = Counter32: 0
.*******.*******.1.13.14 = Counter32: 0
.*******.*******.1.14.1 = Counter32: 0
.*******.*******.1.14.2 = Counter32: 0
.*******.*******.1.14.3 = Counter32: 0
.*******.*******.1.14.4 = Counter32: 0
.*******.*******.1.14.5 = Counter32: 0
.*******.*******.1.14.6 = Counter32: 0
.*******.*******.1.14.7 = Counter32: 0
.*******.*******.1.14.8 = Counter32: 0
.*******.*******.1.14.9 = Counter32: 0
.*******.*******.1.14.10 = Counter32: 0
.*******.*******.1.14.11 = Counter32: 0
.*******.*******.1.14.12 = Counter32: 0
.*******.*******.1.14.13 = Counter32: 0
.*******.*******.1.14.14 = Counter32: 0
.*******.*******.1.15.1 = Counter32: 0
.*******.*******.1.15.2 = Counter32: 0
.*******.*******.1.15.3 = Counter32: 0
.*******.*******.1.15.4 = Counter32: 0
.*******.*******.1.15.5 = Counter32: 0
.*******.*******.1.15.6 = Counter32: 0
.*******.*******.1.15.7 = Counter32: 0
.*******.*******.1.15.8 = Counter32: 0
.*******.*******.1.15.9 = Counter32: 0
.*******.*******.1.15.10 = Counter32: 0
.*******.*******.1.15.11 = Counter32: 0
.*******.*******.1.15.12 = Counter32: 0
.*******.*******.1.15.13 = Counter32: 0
.*******.*******.1.15.14 = Counter32: 0
.*******.*******.1.16.1 = Counter32: 4928
.*******.*******.1.16.2 = Counter32: 28629484
.*******.*******.1.16.3 = Counter32: 3512266
.*******.*******.1.16.4 = Counter32: 3511837
.*******.*******.1.16.5 = Counter32: 3512137
.*******.*******.1.16.6 = Counter32: 3511657
.*******.*******.1.16.7 = Counter32: 0
.*******.*******.1.16.8 = Counter32: 0
.*******.*******.1.16.9 = Counter32: 0
.*******.*******.1.16.10 = Counter32: 12046324
.*******.*******.1.16.11 = Counter32: 0
.*******.*******.1.16.12 = Counter32: 0
.*******.*******.1.16.13 = Counter32: 8535623
.*******.*******.1.16.14 = Counter32: 8534807
.*******.*******.1.17.1 = Counter32: 64
.*******.*******.1.17.2 = Counter32: 301349
.*******.*******.1.17.3 = Counter32: 51972
.*******.*******.1.17.4 = Counter32: 51967
.*******.*******.1.17.5 = Counter32: 51971
.*******.*******.1.17.6 = Counter32: 51967
.*******.*******.1.17.7 = Counter32: 0
.*******.*******.1.17.8 = Counter32: 0
.*******.*******.1.17.9 = Counter32: 0
.*******.*******.1.17.10 = Counter32: 93465
.*******.*******.1.17.11 = Counter32: 0
.*******.*******.1.17.12 = Counter32: 0
.*******.*******.1.17.13 = Counter32: 41507
.*******.*******.1.17.14 = Counter32: 41499
.*******.*******.1.18.1 = Counter32: 0
.*******.*******.1.18.2 = Counter32: 0
.*******.*******.1.18.3 = Counter32: 0
.*******.*******.1.18.4 = Counter32: 0
.*******.*******.1.18.5 = Counter32: 0
.*******.*******.1.18.6 = Counter32: 0
.*******.*******.1.18.7 = Counter32: 0
.*******.*******.1.18.8 = Counter32: 0
.*******.*******.1.18.9 = Counter32: 0
.*******.*******.1.18.10 = Counter32: 0
.*******.*******.1.18.11 = Counter32: 0
.*******.*******.1.18.12 = Counter32: 0
.*******.*******.1.18.13 = Counter32: 0
.*******.*******.1.18.14 = Counter32: 0
.*******.*******.1.19.1 = Counter32: 0
.*******.*******.1.19.2 = Counter32: 0
.*******.*******.1.19.3 = Counter32: 0
.*******.*******.1.19.4 = Counter32: 0
.*******.*******.1.19.5 = Counter32: 0
.*******.*******.1.19.6 = Counter32: 0
.*******.*******.1.19.7 = Counter32: 0
.*******.*******.1.19.8 = Counter32: 0
.*******.*******.1.19.9 = Counter32: 0
.*******.*******.1.19.10 = Counter32: 0
.*******.*******.1.19.11 = Counter32: 0
.*******.*******.1.19.12 = Counter32: 0
.*******.*******.1.19.13 = Counter32: 0
.*******.*******.1.19.14 = Counter32: 0
.*******.*******.1.20.1 = Counter32: 0
.*******.*******.1.20.2 = Counter32: 0
.*******.*******.1.20.3 = Counter32: 0
.*******.*******.1.20.4 = Counter32: 0
.*******.*******.1.20.5 = Counter32: 0
.*******.*******.1.20.6 = Counter32: 0
.*******.*******.1.20.7 = Counter32: 0
.*******.*******.1.20.8 = Counter32: 0
.*******.*******.1.20.9 = Counter32: 0
.*******.*******.1.20.10 = Counter32: 0
.*******.*******.1.20.11 = Counter32: 0
.*******.*******.1.20.12 = Counter32: 0
.*******.*******.1.20.13 = Counter32: 0
.*******.*******.1.20.14 = Counter32: 0
.*******.*******.1.21.1 = Gauge32: 0
.*******.*******.1.21.2 = Gauge32: 0
.*******.*******.1.21.3 = Gauge32: 0
.*******.*******.1.21.4 = Gauge32: 0
.*******.*******.1.21.5 = Gauge32: 0
.*******.*******.1.21.6 = Gauge32: 0
.*******.*******.1.21.7 = Gauge32: 0
.*******.*******.1.21.8 = Gauge32: 0
.*******.*******.1.21.9 = Gauge32: 0
.*******.*******.1.21.10 = Gauge32: 0
.*******.*******.1.21.11 = Gauge32: 0
.*******.*******.1.21.12 = Gauge32: 0
.*******.*******.1.21.13 = Gauge32: 0
.*******.*******.1.21.14 = Gauge32: 0
.*******.2.1.********.1 = OID: .0.0
.*******.2.1.********.2 = OID: .0.0
.*******.2.1.********.3 = OID: .0.0
.*******.2.1.********.4 = OID: .0.0
.*******.2.1.********.5 = OID: .0.0
.*******.2.1.********.6 = OID: .0.0
.*******.2.1.********.7 = OID: .0.0
.*******.2.1.********.8 = OID: .0.0
.*******.2.1.********.9 = OID: .0.0
.*******.2.1.********.10 = OID: .0.0
.*******.2.1.********.11 = OID: .0.0
.*******.2.1.********.12 = OID: .0.0
.*******.2.1.********.13 = OID: .0.0
.*******.2.1.********.14 = OID: .0.0
.*******.*******.********.********** = INTEGER: 14
.*******.*******.********.********** = Hex-STRING: 54 D0 B4 15 B2 FE
.*******.*******.********.********** = IpAddress: **********
.*******.*******.0 = INTEGER: 0
.*******.*******.0 = INTEGER: 0
.*******.*******.0 = Counter32: 0
.*******.*******.0 = Counter32: 0
.*******.*******.0 = Counter32: 0
.*******.*******.0 = Counter32: 0
.*******.*******.0 = Counter32: 0
.*******.*******.0 = Counter32: 0
.*******.*******.0 = Counter32: 0
.*******.********.0 = Counter32: 0
.*******.********.0 = Counter32: 0
.*******.********.0 = Counter32: 0
.*******.********.0 = INTEGER: 0
.*******.********.0 = Counter32: 0
.*******.********.0 = Counter32: 0
.*******.********.0 = Counter32: 0
.*******.********.0 = Counter32: 0
.*******.********.0 = Counter32: 0
.*******.********.0 = Counter32: 0
.*******.********.*********.0.1 = IpAddress: 127.0.0.1
.*******.********.**********.8.2 = IpAddress: **********
.*******.********.*********.0.1 = INTEGER: 1
.*******.********.1.2.********** = INTEGER: 14
.*******.********.*********.0.1 = IpAddress: *********
.*******.********.1.3.********** = IpAddress: ***************
.*******.********.*********.0.1 = INTEGER: 0
.*******.********.1.4.********** = INTEGER: 1
.*******.********.*******.0.0 = IpAddress: 0.0.0.0
.*******.********.**********.8.0 = IpAddress: **********
.*******.********.*******.0.0 = INTEGER: 14
.*******.********.**********.8.0 = INTEGER: 14
.*******.********.*******.0.0 = INTEGER: 1
.*******.********.**********.8.0 = INTEGER: 0
.*******.********.*******.0.0 = IpAddress: **********
.*******.********.**********.8.0 = IpAddress: 0.0.0.0
.*******.********.*******.0.0 = INTEGER: 4
.*******.********.1.8.********** = INTEGER: 3
.*******.********.*******.0.0 = INTEGER: 2
.*******.********.**********.8.0 = INTEGER: 2
.*******.********.********.0.0 = IpAddress: 0.0.0.0
.*******.********.1.11.********** = IpAddress: ***************
.*******.********.1.13.0.0.0.0 = OID: .0.0
.*******.********.1.13.********** = OID: .0.0
.*******.*******2.********72.17.8.1 = INTEGER: 14
.*******.*******2.1.2.14.********** = Hex-STRING: 54 D0 B4 15 B2 FE
.*******.**************4.********** = IpAddress: **********
.*******.***************.********** = INTEGER: 3
.*******.*******3.0 = Counter32: 0
.*******.*******5.1.4.14.1.4.********** = Hex-STRING: 54 D0 B4 15 B2 FE
.*******.*******5.1.4.14.2.16.254.128.0.0.0.0.0.0.0.67.62.107.220.2.152.149 = ""
.*******.*******5.1.4.14.2.16.254.128.0.0.0.0.0.0.4.155.158.171.254.27.193.234 = Hex-STRING: 88 A4 23 00 1A AE
.*******.*******5.1.4.14.2.16.254.128.0.0.0.0.0.0.86.238.107.16.254.213.19.77 = ""
.*******.*******5.1.4.14.2.16.254.128.0.0.0.0.0.0.185.58.94.15.231.179.98.19 = Hex-STRING: 88 A4 23 00 19 17
.*******.*******5.1.5.14.1.4.********** = Timeticks: (7419144) 20:36:31.44
.*******.*******5.1.5.14.2.16.254.128.0.0.0.0.0.0.0.67.62.107.220.2.152.149 = Timeticks: (7419145) 20:36:31.45
.*******.*******5.1.5.14.2.16.254.128.0.0.0.0.0.0.4.155.158.171.254.27.193.234 = Timeticks: (7419145) 20:36:31.45
.*******.*******5.1.5.14.2.16.254.128.0.0.0.0.0.0.86.238.107.16.254.213.19.77 = Timeticks: (7419145) 20:36:31.45
.*******.*******5.1.5.14.2.16.254.128.0.0.0.0.0.0.185.58.94.15.231.179.98.19 = Timeticks: (7419145) 20:36:31.45
.*******.*******5.1.6.14.1.4.********** = INTEGER: 3
.*******.*******5.1.6.14.2.16.254.128.0.0.0.0.0.0.0.67.62.107.220.2.152.149 = INTEGER: 2
.*******.*******5.1.6.14.2.16.254.128.0.0.0.0.0.0.4.155.158.171.254.27.193.234 = INTEGER: 3
.*******.*******5.1.6.14.2.16.254.128.0.0.0.0.0.0.86.238.107.16.254.213.19.77 = INTEGER: 2
.*******.*******5.1.6.14.2.16.254.128.0.0.0.0.0.0.185.58.94.15.231.179.98.19 = INTEGER: 3
.*******.*******5.1.7.14.1.4.********** = INTEGER: 3
.*******.*******5.1.7.14.2.16.254.128.0.0.0.0.0.0.0.67.62.107.220.2.152.149 = INTEGER: 6
.*******.*******5.1.7.14.2.16.254.128.0.0.0.0.0.0.4.155.158.171.254.27.193.234 = INTEGER: 2
.*******.*******5.1.7.14.2.16.254.128.0.0.0.0.0.0.86.238.107.16.254.213.19.77 = INTEGER: 6
.*******.*******5.1.7.14.2.16.254.128.0.0.0.0.0.0.185.58.94.15.231.179.98.19 = INTEGER: 2
.*******.*******5.1.8.14.1.4.********** = INTEGER: 1
.*******.*******5.1.8.14.2.16.254.128.0.0.0.0.0.0.0.67.62.107.220.2.152.149 = INTEGER: 1
.*******.*******5.1.8.14.2.16.254.128.0.0.0.0.0.0.4.155.158.171.254.27.193.234 = INTEGER: 1
.*******.*******5.1.8.14.2.16.254.128.0.0.0.0.0.0.86.238.107.16.254.213.19.77 = INTEGER: 1
.*******.*******5.1.8.14.2.16.254.128.0.0.0.0.0.0.185.58.94.15.231.179.98.19 = INTEGER: 1
.*******.2.1.5.1.0 = Counter32: 0
.*******.2.1.5.2.0 = Counter32: 0
.*******.2.1.5.3.0 = Counter32: 0
.*******.2.1.5.4.0 = Counter32: 0
.*******.2.1.5.5.0 = Counter32: 0
.*******.2.1.5.6.0 = Counter32: 0
.*******.2.1.5.7.0 = Counter32: 0
.*******.2.1.5.8.0 = Counter32: 0
.*******.2.1.5.9.0 = Counter32: 0
.*******.2.1.5.10.0 = Counter32: 0
.*******.2.1.5.11.0 = Counter32: 0
.*******.2.1.5.12.0 = Counter32: 0
.*******.2.1.5.13.0 = Counter32: 0
.*******.2.1.5.14.0 = Counter32: 0
.*******.2.1.5.15.0 = Counter32: 0
.*******.2.1.5.16.0 = Counter32: 0
.*******.2.1.5.17.0 = Counter32: 0
.*******.2.1.5.18.0 = Counter32: 0
.*******.2.1.5.19.0 = Counter32: 0
.*******.2.1.5.20.0 = Counter32: 0
.*******.2.1.5.21.0 = Counter32: 0
.*******.2.1.5.22.0 = Counter32: 0
.*******.2.1.5.23.0 = Counter32: 0
.*******.2.1.5.24.0 = Counter32: 0
.*******.2.1.5.25.0 = Counter32: 0
.*******.2.1.5.26.0 = Counter32: 0
.*******.2.1.5.29.1.2.1 = Counter32: 0
.*******.2.1.5.29.1.2.2 = Counter32: 0
.*******.2.1.5.29.1.3.1 = Counter32: 0
.*******.2.1.5.29.1.3.2 = Counter32: 0
.*******.2.1.5.29.1.4.1 = Counter32: 0
.*******.2.1.5.29.1.4.2 = Counter32: 0
.*******.2.1.5.29.1.5.1 = Counter32: 0
.*******.2.1.5.29.1.5.2 = Counter32: 0
.*******.2.1.5.30.1.3.1.0 = Counter32: 0
.*******.2.1.5.30.1.3.1.3 = Counter32: 0
.*******.2.1.5.30.1.3.1.4 = Counter32: 0
.*******.2.1.5.30.1.3.1.5 = Counter32: 0
.*******.2.1.5.30.1.3.1.8 = Counter32: 0
.*******.2.1.5.30.1.3.1.11 = Counter32: 0
.*******.2.1.5.30.1.3.1.12 = Counter32: 0
.*******.2.1.5.30.1.3.1.13 = Counter32: 0
.*******.2.1.5.30.1.3.1.14 = Counter32: 0
.*******.2.1.5.30.1.3.1.17 = Counter32: 0
.*******.2.1.5.30.1.3.1.18 = Counter32: 0
.*******.2.1.5.30.1.3.2.1 = Counter32: 0
.*******.2.1.5.30.1.3.2.2 = Counter32: 0
.*******.2.1.5.30.1.3.2.3 = Counter32: 0
.*******.2.1.5.30.1.3.2.4 = Counter32: 0
.*******.2.1.5.30.1.3.2.128 = Counter32: 0
.*******.2.1.5.30.1.3.2.129 = Counter32: 0
.*******.2.1.5.30.1.3.2.130 = Counter32: 0
.*******.2.1.5.30.1.3.2.131 = Counter32: 0
.*******.2.1.5.30.1.3.2.132 = Counter32: 0
.*******.2.1.5.30.1.3.2.133 = Counter32: 0
.*******.2.1.5.30.1.3.2.134 = Counter32: 0
.*******.2.1.5.30.1.3.2.135 = Counter32: 0
.*******.2.1.5.30.1.3.2.136 = Counter32: 0
.*******.2.1.5.30.1.3.2.137 = Counter32: 0
.*******.2.1.5.30.1.4.1.0 = Counter32: 0
.*******.2.1.5.30.1.4.1.3 = Counter32: 0
.*******.2.1.5.30.1.4.1.4 = Counter32: 0
.*******.2.1.5.30.1.4.1.5 = Counter32: 0
.*******.2.1.5.30.1.4.1.8 = Counter32: 0
.*******.2.1.5.30.1.4.1.11 = Counter32: 0
.*******.2.1.5.30.1.4.1.12 = Counter32: 0
.*******.2.1.5.30.1.4.1.13 = Counter32: 0
.*******.2.1.5.30.1.4.1.14 = Counter32: 0
.*******.2.1.5.30.1.4.1.17 = Counter32: 0
.*******.2.1.5.30.1.4.1.18 = Counter32: 0
.*******.2.1.5.30.1.4.2.1 = Counter32: 0
.*******.2.1.5.30.1.4.2.2 = Counter32: 0
.*******.2.1.5.30.1.4.2.3 = Counter32: 0
.*******.2.1.5.30.1.4.2.4 = Counter32: 0
.*******.2.1.5.30.1.4.2.128 = Counter32: 0
.*******.2.1.5.30.1.4.2.129 = Counter32: 0
.*******.2.1.5.30.1.4.2.131 = Counter32: 0
.*******.2.1.5.30.1.4.2.132 = Counter32: 0
.*******.2.1.5.30.1.4.2.133 = Counter32: 0
.*******.2.1.5.30.1.4.2.135 = Counter32: 0
.*******.2.1.5.30.1.4.2.136 = Counter32: 0
.*******.2.1.5.30.1.4.2.137 = Counter32: 0
.*******.2.1.6.1.0 = INTEGER: 0
.*******.2.1.6.2.0 = INTEGER: 0
.*******.2.1.6.3.0 = INTEGER: 0
.*******.2.1.6.4.0 = INTEGER: 0
.*******.*******.0 = Counter32: 0
.*******.*******.0 = Counter32: 0
.*******.*******.0 = Counter32: 0
.*******.*******.0 = Counter32: 0
.*******.*******.0 = Gauge32: 0
.*******.********.0 = Counter32: 0
.*******.********.0 = Counter32: 0
.*******.********.0 = Counter32: 0
.*******.********.*******.********.0.0.0.0 = INTEGER: 2
.*******.********.*******.********.0.0.0.0 = INTEGER: 2
.*******.********.*******.0.0.443.0.0.0.0.0 = INTEGER: 2
.*******.********.*******.********.0.0.0.0 = IpAddress: 0.0.0.0
.*******.********.*******.********.0.0.0.0 = IpAddress: 0.0.0.0
.*******.********.*******.0.0.443.0.0.0.0.0 = IpAddress: 0.0.0.0
.*******.********.*******.********.0.0.0.0 = INTEGER: 22
.*******.********.*******.********.0.0.0.0 = INTEGER: 80
.*******.********.*******.0.0.443.0.0.0.0.0 = INTEGER: 443
.*******.********.*******.********.0.0.0.0 = IpAddress: 0.0.0.0
.*******.********.*******.********.0.0.0.0 = IpAddress: 0.0.0.0
.*******.********.*******.0.0.443.0.0.0.0.0 = IpAddress: 0.0.0.0
.*******.********.*******.********.0.0.0.0 = INTEGER: 0
.*******.********.*******.********.0.0.0.0 = INTEGER: 0
.*******.********.*******.0.0.443.0.0.0.0.0 = INTEGER: 0
.*******.*******.0 = Counter32: 0
.*******.*******.0 = Counter32: 0
.*******.*******.0 = Counter32: 0
.*******.2.1.7.4.0 = Counter32: 0
.*******.2.1.7.5.*******.0.0.161 = IpAddress: 0.0.0.0
.*******.2.1.7.5.*******.0.0.161 = INTEGER: 161
.*******.2.1.11.1.0 = Counter32: 28759
.*******.2.1.11.2.0 = Counter32: 28753
.*******.2.1.11.3.0 = Counter32: 0
.*******.2.1.11.4.0 = Counter32: 6
.*******.2.1.11.5.0 = Counter32: 0
.*******.2.1.11.6.0 = Counter32: 0
.*******.2.1.11.8.0 = Counter32: 0
.*******.2.1.11.9.0 = Counter32: 0
.*******.2.1.11.10.0 = Counter32: 0
.*******.2.1.11.11.0 = Counter32: 0
.*******.2.1.11.12.0 = Counter32: 0
.*******.2.1.11.13.0 = Counter32: 193473
.*******.2.1.11.14.0 = Counter32: 0
.*******.2.1.11.15.0 = Counter32: 5684
.*******.2.1.11.16.0 = Counter32: 7075
.*******.2.1.11.17.0 = Counter32: 0
.*******.2.1.11.18.0 = Counter32: 0
.*******.2.1.11.19.0 = Counter32: 0
.*******.2.1.11.20.0 = Counter32: 0
.*******.2.1.11.21.0 = Counter32: 0
.*******.2.1.11.22.0 = Counter32: 0
.*******.2.1.11.24.0 = Counter32: 0
.*******.2.1.11.25.0 = Counter32: 0
.*******.2.1.11.26.0 = Counter32: 0
.*******.2.1.11.27.0 = Counter32: 0
.*******.2.1.11.28.0 = Counter32: 28755
.*******.2.1.11.29.0 = Counter32: 0
.*******.2.1.11.30.0 = INTEGER: 2
.*******.2.1.11.31.0 = Counter32: 0
.*******.2.1.11.32.0 = Counter32: 0
.*******.2.1.25.1.1.0 = Timeticks: (7426599) 20:37:45.99
.*******.2.1.25.1.2.0 = Hex-STRING: 07 E8 07 1D 14 30 0E 00 2B 02 00
.*******.2.1.25.1.3.0 = INTEGER: 393216
.*******.2.1.25.1.4.0 = STRING: "console=ttyS0,115200"
.*******.2.1.25.1.5.0 = Gauge32: 0
.*******.2.1.25.1.7.0 = INTEGER: 0
.*******.2.1.25.2.2.0 = INTEGER: 124128
.*******.2.1.25.2.3.1.1.1 = INTEGER: 1
.*******.2.1.25.2.3.1.1.3 = INTEGER: 3
.*******.2.1.25.2.3.1.1.6 = INTEGER: 6
.*******.2.1.25.2.3.1.1.7 = INTEGER: 7
.*******.2.1.25.2.3.1.1.8 = INTEGER: 8
.*******.2.1.25.2.3.1.1.10 = INTEGER: 10
.*******.2.1.25.2.3.1.1.11 = INTEGER: 11
.*******.2.1.25.2.3.1.1.31 = INTEGER: 31
.*******.2.1.25.2.3.1.1.32 = INTEGER: 32
.*******.2.1.25.2.3.1.1.33 = INTEGER: 33
.*******.2.1.25.2.3.1.1.34 = INTEGER: 34
.*******.2.1.25.2.3.1.1.35 = INTEGER: 35
.*******.2.1.25.2.3.1.1.36 = INTEGER: 36
.*******.2.1.25.2.3.1.2.1 = OID: .*******.2.1.25.2.1.2
.*******.2.1.25.2.3.1.2.3 = OID: .*******.2.1.25.2.1.3
.*******.2.1.25.2.3.1.2.6 = OID: .*******.2.1.25.2.1.1
.*******.2.1.25.2.3.1.2.7 = OID: .*******.2.1.25.2.1.1
.*******.2.1.25.2.3.1.2.8 = OID: .*******.2.1.25.2.1.1
.*******.2.1.25.2.3.1.2.10 = OID: .*******.2.1.25.2.1.3
.*******.2.1.25.2.3.1.2.11 = OID: .*******.2.1.25.2.1.1
.*******.2.1.25.2.3.1.2.31 = OID: .*******.2.1.25.2.1.4
.*******.2.1.25.2.3.1.2.32 = OID: .*******.2.1.25.2.1.4
.*******.2.1.25.2.3.1.2.33 = OID: .*******.2.1.25.2.1.4
.*******.2.1.25.2.3.1.2.34 = OID: .*******.2.1.25.2.1.4
.*******.2.1.25.2.3.1.2.35 = OID: .*******.2.1.25.2.1.4
.*******.2.1.25.2.3.1.2.36 = OID: .*******.2.1.25.2.1.4
.*******.2.1.25.2.3.1.3.1 = STRING: "Physical memory"
.*******.2.1.25.2.3.1.3.3 = STRING: "Virtual memory"
.*******.2.1.25.2.3.1.3.6 = STRING: "Memory buffers"
.*******.2.1.25.2.3.1.3.7 = STRING: "Cached memory"
.*******.2.1.25.2.3.1.3.8 = STRING: "Shared memory"
.*******.2.1.25.2.3.1.3.10 = STRING: "Swap space"
.*******.2.1.25.2.3.1.3.11 = STRING: "Available memory"
.*******.2.1.25.2.3.1.3.31 = STRING: "/rom"
.*******.2.1.25.2.3.1.3.32 = STRING: "/tmp"
.*******.2.1.25.2.3.1.3.33 = STRING: "/overlay"
.*******.2.1.25.2.3.1.3.34 = STRING: "/"
.*******.2.1.25.2.3.1.3.35 = STRING: "/log"
.*******.2.1.25.2.3.1.3.36 = STRING: "/sys/fs/bpf"
.*******.2.1.25.2.3.1.4.1 = INTEGER: 1024
.*******.2.1.25.2.3.1.4.3 = INTEGER: 1024
.*******.2.1.25.2.3.1.4.6 = INTEGER: 1024
.*******.2.1.25.2.3.1.4.7 = INTEGER: 1024
.*******.2.1.25.2.3.1.4.8 = INTEGER: 1024
.*******.2.1.25.2.3.1.4.10 = INTEGER: 1024
.*******.2.1.25.2.3.1.4.11 = INTEGER: 1024
.*******.2.1.25.2.3.1.4.31 = INTEGER: 65536
.*******.2.1.25.2.3.1.4.32 = INTEGER: 4096
.*******.2.1.25.2.3.1.4.33 = INTEGER: 4096
.*******.2.1.25.2.3.1.4.34 = INTEGER: 4096
.*******.2.1.25.2.3.1.4.35 = INTEGER: 4096
.*******.2.1.25.2.3.1.4.36 = INTEGER: 4096
.*******.2.1.25.2.3.1.5.1 = INTEGER: 124128
.*******.2.1.25.2.3.1.5.3 = INTEGER: 124128
.*******.2.1.25.2.3.1.5.6 = INTEGER: 124128
.*******.2.1.25.2.3.1.5.7 = INTEGER: 22256
.*******.2.1.25.2.3.1.5.8 = INTEGER: 88
.*******.2.1.25.2.3.1.5.10 = INTEGER: 0
.*******.2.1.25.2.3.1.5.11 = INTEGER: 58508
.*******.2.1.25.2.3.1.5.31 = INTEGER: 128
.*******.2.1.25.2.3.1.5.32 = INTEGER: 15516
.*******.2.1.25.2.3.1.5.33 = INTEGER: 1328
.*******.2.1.25.2.3.1.5.34 = INTEGER: 1328
.*******.2.1.25.2.3.1.5.35 = INTEGER: 144
.*******.2.1.25.2.3.1.5.36 = INTEGER: 0
.*******.2.1.25.2.3.1.6.1 = INTEGER: 52140
.*******.2.1.25.2.3.1.6.3 = INTEGER: 52140
.*******.2.1.25.2.3.1.6.6 = INTEGER: 0
.*******.2.1.25.2.3.1.6.7 = INTEGER: 22256
.*******.2.1.25.2.3.1.6.8 = INTEGER: 88
.*******.2.1.25.2.3.1.6.10 = INTEGER: 0
.*******.2.1.25.2.3.1.6.11 = INTEGER: 0
.*******.2.1.25.2.3.1.6.31 = INTEGER: 128
.*******.2.1.25.2.3.1.6.32 = INTEGER: 22
.*******.2.1.25.2.3.1.6.33 = INTEGER: 99
.*******.2.1.25.2.3.1.6.34 = INTEGER: 99
.*******.2.1.25.2.3.1.6.35 = INTEGER: 64
.*******.2.1.25.2.3.1.6.36 = INTEGER: 0
.*******.2.1.25.3.2.1.1.196608 = INTEGER: 196608
.*******.2.1.25.3.2.1.1.262145 = INTEGER: 262145
.*******.2.1.25.3.2.1.1.262146 = INTEGER: 262146
.*******.2.1.25.3.2.1.1.262147 = INTEGER: 262147
.*******.2.1.25.3.2.1.1.262148 = INTEGER: 262148
.*******.2.1.25.3.2.1.1.262149 = INTEGER: 262149
.*******.2.1.25.3.2.1.1.262150 = INTEGER: 262150
.*******.2.1.25.3.2.1.1.262151 = INTEGER: 262151
.*******.2.1.25.3.2.1.1.262152 = INTEGER: 262152
.*******.2.1.25.3.2.1.1.262153 = INTEGER: 262153
.*******.2.1.25.3.2.1.1.262154 = INTEGER: 262154
.*******.2.1.25.3.2.1.1.262155 = INTEGER: 262155
.*******.2.1.25.3.2.1.1.262156 = INTEGER: 262156
.*******.2.1.25.3.2.1.1.262157 = INTEGER: 262157
.*******.2.1.25.3.2.1.1.262158 = INTEGER: 262158
.*******.2.1.25.3.2.1.2.196608 = OID: .*******.2.1.25.3.1.3
.*******.2.1.25.3.*******62145 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62146 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62147 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62148 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62149 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62150 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62151 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62152 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62153 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62154 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62155 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62156 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62157 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******62158 = OID: .*******.2.1.25.3.1.4
.*******.2.1.25.3.*******96608 = ""
.*******.2.1.25.3.2.1.3.262145 = STRING: "network interface lo"
.*******.2.1.25.3.2.1.3.262146 = STRING: "network interface eth0"
.*******.2.1.25.3.2.1.3.262147 = STRING: "network interface port1"
.*******.2.1.25.3.2.1.3.262148 = STRING: "network interface port2"
.*******.2.1.25.3.2.1.3.262149 = STRING: "network interface port3"
.*******.2.1.25.3.2.1.3.262150 = STRING: "network interface port4"
.*******.2.1.25.3.2.1.3.262151 = STRING: "network interface port5"
.*******.2.1.25.3.2.1.3.262152 = STRING: "network interface port6"
.*******.2.1.25.3.2.1.3.262153 = STRING: "network interface port7"
.*******.2.1.25.3.2.1.3.262154 = STRING: "network interface port8"
.*******.2.1.25.3.2.1.3.262155 = STRING: "network interface sfp1"
.*******.2.1.25.3.2.1.3.262156 = STRING: "network interface sfp2"
.*******.2.1.25.3.2.1.3.262157 = STRING: "network interface br0"
.*******.2.1.25.3.2.1.3.262158 = STRING: "network interface br0.1"
.*******.2.1.25.3.********6608 = OID: .0.0
.*******.2.1.25.3.*******62145 = OID: .0.0
.*******.2.1.25.3.*******62146 = OID: .0.0
.*******.2.1.25.3.*******62147 = OID: .0.0
.*******.2.1.25.3.*******62148 = OID: .0.0
.*******.2.1.25.3.*******62149 = OID: .0.0
.*******.2.1.25.3.*******62150 = OID: .0.0
.*******.2.1.25.3.*******62151 = OID: .0.0
.*******.2.1.25.3.*******62152 = OID: .0.0
.*******.2.1.25.3.*******62153 = OID: .0.0
.*******.2.1.25.3.*******62154 = OID: .0.0
.*******.2.1.25.3.*******62155 = OID: .0.0
.*******.2.1.25.3.*******62156 = OID: .0.0
.*******.2.1.25.3.*******62157 = OID: .0.0
.*******.2.1.25.3.*******62158 = OID: .0.0
.*******.2.1.25.3.2.1.5.196608 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262145 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262146 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262147 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262148 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262149 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262150 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262151 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262152 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262153 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262154 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262155 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262156 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262157 = INTEGER: 2
.*******.2.1.25.3.2.1.5.262158 = INTEGER: 2
.*******.2.1.25.3.2.1.6.262145 = Counter32: 0
.*******.2.1.25.3.2.1.6.262146 = Counter32: 0
.*******.2.1.25.3.2.1.6.262147 = Counter32: 0
.*******.2.1.25.3.2.1.6.262148 = Counter32: 0
.*******.2.1.25.3.2.1.6.262149 = Counter32: 0
.*******.2.1.25.3.2.1.6.262150 = Counter32: 0
.*******.2.1.25.3.2.1.6.262151 = Counter32: 0
.*******.2.1.25.3.2.1.6.262152 = Counter32: 0
.*******.2.1.25.3.2.1.6.262153 = Counter32: 0
.*******.2.1.25.3.2.1.6.262154 = Counter32: 0
.*******.2.1.25.3.2.1.6.262155 = Counter32: 0
.*******.2.1.25.3.2.1.6.262156 = Counter32: 0
.*******.2.1.25.3.2.1.6.262157 = Counter32: 0
.*******.2.1.25.3.2.1.6.262158 = Counter32: 0
.*******.2.1.25.3.3.1.1.196608 = OID: .0.0
.*******.2.1.25.3.3.1.2.196608 = INTEGER: 7
.*******.2.1.25.3.4.1.1.262145 = INTEGER: 1
.*******.2.1.25.3.4.1.1.262146 = INTEGER: 2
.*******.2.1.25.3.4.1.1.262147 = INTEGER: 3
.*******.2.1.25.3.4.1.1.262148 = INTEGER: 4
.*******.2.1.25.3.4.1.1.262149 = INTEGER: 5
.*******.2.1.25.3.4.1.1.262150 = INTEGER: 6
.*******.2.1.25.3.4.1.1.262151 = INTEGER: 7
.*******.2.1.25.3.4.1.1.262152 = INTEGER: 8
.*******.2.1.25.3.4.1.1.262153 = INTEGER: 9
.*******.2.1.25.3.4.1.1.262154 = INTEGER: 10
.*******.2.1.25.3.4.1.1.262155 = INTEGER: 11
.*******.2.1.25.3.4.1.1.262156 = INTEGER: 12
.*******.2.1.25.3.4.1.1.262157 = INTEGER: 13
.*******.2.1.25.3.4.1.1.262158 = INTEGER: 14
.*******.2.1.25.3.******* = INTEGER: 1
.*******.2.1.25.3.8.1.1.2 = INTEGER: 2
.*******.2.1.25.3.8.1.1.3 = INTEGER: 3
.*******.2.1.25.3.8.1.1.4 = INTEGER: 4
.*******.2.1.25.3.8.1.1.5 = INTEGER: 5
.*******.2.1.25.3.8.1.1.6 = INTEGER: 6
.*******.2.1.25.3.******* = STRING: "/rom"
.*******.2.1.25.3.8.1.2.2 = STRING: "/tmp"
.*******.2.1.25.3.8.1.2.3 = STRING: "/overlay"
.*******.2.1.25.3.8.1.2.4 = STRING: "/"
.*******.2.1.25.3.8.1.2.5 = STRING: "/log"
.*******.2.1.25.3.8.1.2.6 = STRING: "/sys/fs/bpf"
.*******.2.1.25.3.******* = ""
.*******.2.1.25.3.8.1.3.2 = ""
.*******.2.1.25.3.8.1.3.3 = ""
.*******.2.1.25.3.8.1.3.4 = ""
.*******.2.1.25.3.8.1.3.5 = ""
.*******.2.1.25.3.8.1.3.6 = ""
.*******.2.1.25.3.8.1.4.1 = OID: .*******.2.1.25.3.9.1
.*******.2.1.25.3.8.1.4.2 = OID: .*******.2.1.25.3.9.1
.*******.2.1.25.3.8.1.4.3 = OID: .*******.2.1.25.3.9.1
.*******.2.1.25.3.8.1.4.4 = OID: .*******.2.1.25.3.9.1
.*******.2.1.25.3.8.1.4.5 = OID: .*******.2.1.25.3.9.1
.*******.2.1.25.3.8.1.4.6 = OID: .*******.2.1.25.3.9.1
.*******.2.1.25.3.8.1.5.1 = INTEGER: 2
.*******.2.1.25.3.8.1.5.2 = INTEGER: 1
.*******.2.1.25.3.8.1.5.3 = INTEGER: 1
.*******.2.1.25.3.8.1.5.4 = INTEGER: 1
.*******.2.1.25.3.8.1.5.5 = INTEGER: 1
.*******.2.1.25.3.8.1.5.6 = INTEGER: 1
.*******.2.1.25.3.8.1.6.1 = INTEGER: 2
.*******.2.1.25.3.8.1.6.2 = INTEGER: 2
.*******.2.1.25.3.8.1.6.3 = INTEGER: 2
.*******.2.1.25.3.8.1.6.4 = INTEGER: 1
.*******.2.1.25.3.8.1.6.5 = INTEGER: 2
.*******.2.1.25.3.8.1.6.6 = INTEGER: 2
.*******.2.1.25.3.8.1.7.1 = INTEGER: 31
.*******.2.1.25.3.8.1.7.2 = INTEGER: 32
.*******.2.1.25.3.8.1.7.3 = INTEGER: 33
.*******.2.1.25.3.8.1.7.4 = INTEGER: 34
.*******.2.1.25.3.8.1.7.5 = INTEGER: 35
.*******.2.1.25.3.8.1.7.6 = INTEGER: 36
.*******.2.1.25.3.8.1.8.1 = Hex-STRING: 00 00 01 01 00 00 00 00
.*******.2.1.25.3.8.1.8.2 = Hex-STRING: 00 00 01 01 00 00 00 00
.*******.2.1.25.3.8.1.8.3 = Hex-STRING: 00 00 01 01 00 00 00 00
.*******.2.1.25.3.8.1.8.4 = Hex-STRING: 00 00 01 01 00 00 00 00
.*******.2.1.25.3.8.1.8.5 = Hex-STRING: 00 00 01 01 00 00 00 00
.*******.2.1.25.3.8.1.8.6 = Hex-STRING: 00 00 01 01 00 00 00 00
.*******.2.1.25.3.8.1.9.1 = Hex-STRING: 00 00 01 01 00 00 00 00
.*******.2.1.25.3.8.1.9.2 = Hex-STRING: 00 00 01 01 00 00 00 00
.*******.2.1.25.3.8.1.9.3 = Hex-STRING: 00 00 01 01 00 00 00 00
.*******.2.1.25.3.8.1.9.4 = Hex-STRING: 00 00 01 01 00 00 00 00
.*******.2.1.25.3.8.1.9.5 = Hex-STRING: 00 00 01 01 00 00 00 00
.*******.2.1.25.3.8.1.9.6 = Hex-STRING: 00 00 01 01 00 00 00 00
.*******.2.1.31.1.1.1.1.1 = STRING: "lo"
.*******.2.1.31.1.1.1.1.2 = STRING: "eth0"
.*******.2.1.31.1.1.1.1.3 = STRING: "port1"
.*******.2.1.31.1.1.1.1.4 = STRING: "port2"
.*******.2.1.31.1.1.1.1.5 = STRING: "port3"
.*******.2.1.31.1.1.1.1.6 = STRING: "port4"
.*******.2.1.31.1.1.1.1.7 = STRING: "port5"
.*******.2.1.31.1.1.1.1.8 = STRING: "port6"
.*******.2.1.31.1.1.1.1.9 = STRING: "port7"
.*******.2.1.31.1.1.1.1.10 = STRING: "port8"
.*******.2.1.31.1.1.1.1.11 = STRING: "sfp1"
.*******.2.1.31.1.1.1.1.12 = STRING: "sfp2"
.*******.2.1.31.1.1.1.1.13 = STRING: "br0"
.*******.2.1.31.1.1.1.1.14 = STRING: "br0.1"
.*******.2.1.31.1.1.1.2.1 = Counter32: 0
.*******.2.1.31.1.1.1.2.2 = Counter32: 0
.*******.2.1.31.1.1.1.2.3 = Counter32: 0
.*******.2.1.31.1.1.1.2.4 = Counter32: 0
.*******.2.1.31.1.1.1.2.5 = Counter32: 0
.*******.2.1.31.1.1.1.2.6 = Counter32: 0
.*******.2.1.31.1.1.1.2.7 = Counter32: 0
.*******.2.1.31.1.1.1.2.8 = Counter32: 0
.*******.2.1.31.1.1.1.2.9 = Counter32: 0
.*******.2.1.31.1.1.1.2.10 = Counter32: 0
.*******.2.1.31.1.1.1.2.11 = Counter32: 0
.*******.2.1.31.1.1.1.2.12 = Counter32: 0
.*******.2.1.31.1.1.1.2.13 = Counter32: 147
.*******.2.1.31.1.1.1.2.14 = Counter32: 147
.*******.2.1.31.1.1.1.3.1 = Counter32: 0
.*******.2.1.31.1.1.1.3.2 = Counter32: 0
.*******.2.1.31.1.1.1.3.3 = Counter32: 0
.*******.2.1.31.1.1.1.3.4 = Counter32: 0
.*******.2.1.31.1.1.1.3.5 = Counter32: 0
.*******.2.1.31.1.1.1.3.6 = Counter32: 0
.*******.2.1.31.1.1.1.3.7 = Counter32: 0
.*******.2.1.31.1.1.1.3.8 = Counter32: 0
.*******.2.1.31.1.1.1.3.9 = Counter32: 0
.*******.2.1.31.1.1.1.3.10 = Counter32: 0
.*******.2.1.31.1.1.1.3.11 = Counter32: 0
.*******.2.1.31.1.1.1.3.12 = Counter32: 0
.*******.2.1.31.1.1.1.3.13 = Counter32: 0
.*******.2.1.31.1.1.1.3.14 = Counter32: 0
.*******.2.1.31.1.1.1.4.1 = Counter32: 0
.*******.2.1.31.1.1.1.4.2 = Counter32: 0
.*******.2.1.31.1.1.1.4.3 = Counter32: 0
.*******.2.1.31.1.1.1.4.4 = Counter32: 0
.*******.2.1.31.1.1.1.4.5 = Counter32: 0
.*******.2.1.31.1.1.1.4.6 = Counter32: 0
.*******.2.1.31.1.1.1.4.7 = Counter32: 0
.*******.2.1.31.1.1.1.4.8 = Counter32: 0
.*******.2.1.31.1.1.1.4.9 = Counter32: 0
.*******.2.1.31.1.1.1.4.10 = Counter32: 0
.*******.2.1.31.1.1.1.4.11 = Counter32: 0
.*******.2.1.31.1.1.1.4.12 = Counter32: 0
.*******.2.1.31.1.1.1.4.13 = Counter32: 0
.*******.2.1.31.1.1.1.4.14 = Counter32: 0
.*******.2.1.31.1.1.1.5.1 = Counter32: 0
.*******.2.1.31.1.1.1.5.2 = Counter32: 0
.*******.2.1.31.1.1.1.5.3 = Counter32: 0
.*******.2.1.31.1.1.1.5.4 = Counter32: 0
.*******.2.1.31.1.1.1.5.5 = Counter32: 0
.*******.2.1.31.1.1.1.5.6 = Counter32: 0
.*******.2.1.31.1.1.1.5.7 = Counter32: 0
.*******.2.1.31.1.1.1.5.8 = Counter32: 0
.*******.2.1.31.1.1.1.5.9 = Counter32: 0
.*******.2.1.31.1.1.1.5.10 = Counter32: 0
.*******.2.1.31.1.1.1.5.11 = Counter32: 0
.*******.2.1.31.1.1.1.5.12 = Counter32: 0
.*******.2.1.31.1.1.1.5.13 = Counter32: 0
.*******.2.1.31.1.1.1.5.14 = Counter32: 0
.*******.2.1.31.1.1.1.6.1 = Counter64: 4928
.*******.2.1.31.1.1.1.6.2 = Counter64: 4447431
.*******.2.1.31.1.1.1.6.3 = Counter64: 2708
.*******.2.1.31.1.1.1.6.4 = Counter64: 3904
.*******.2.1.31.1.1.1.6.5 = Counter64: 2932
.*******.2.1.31.1.1.1.6.6 = Counter64: 16930
.*******.2.1.31.1.1.1.6.7 = Counter64: 0
.*******.2.1.31.1.1.1.6.8 = Counter64: 0
.*******.2.1.31.1.1.1.6.9 = Counter64: 0
.*******.2.1.31.1.1.1.6.10 = Counter64: 3529957
.*******.2.1.31.1.1.1.6.11 = Counter64: 0
.*******.2.1.31.1.1.1.6.12 = Counter64: 0
.*******.2.1.31.1.1.1.6.13 = Counter64: 3177529
.*******.2.1.31.1.1.1.6.14 = Counter64: 3177529
.*******.2.1.31.1.1.1.7.1 = Counter64: 64
.*******.2.1.31.1.1.1.7.2 = Counter64: 49500
.*******.2.1.31.1.1.1.7.3 = Counter64: 49
.*******.2.1.31.1.1.1.7.4 = Counter64: 75
.*******.2.1.31.1.1.1.7.5 = Counter64: 38
.*******.2.1.31.1.1.1.7.6 = Counter64: 343
.*******.2.1.31.1.1.1.7.7 = Counter64: 0
.*******.2.1.31.1.1.1.7.8 = Counter64: 0
.*******.2.1.31.1.1.1.7.9 = Counter64: 0
.*******.2.1.31.1.1.1.7.10 = Counter64: 48995
.*******.2.1.31.1.1.1.7.11 = Counter64: 0
.*******.2.1.31.1.1.1.7.12 = Counter64: 0
.*******.2.1.31.1.1.1.7.13 = Counter64: 41116
.*******.2.1.31.1.1.1.7.14 = Counter64: 41116
.*******.2.1.31.1.1.1.8.1 = Counter64: 0
.*******.2.1.31.1.1.1.8.2 = Counter64: 0
.*******.2.1.31.1.1.1.8.3 = Counter64: 0
.*******.2.1.31.1.1.1.8.4 = Counter64: 0
.*******.2.1.31.1.1.1.8.5 = Counter64: 0
.*******.2.1.31.1.1.1.8.6 = Counter64: 0
.*******.2.1.31.1.1.1.8.7 = Counter64: 0
.*******.2.1.31.1.1.1.8.8 = Counter64: 0
.*******.2.1.31.1.1.1.8.9 = Counter64: 0
.*******.2.1.31.1.1.1.8.10 = Counter64: 0
.*******.2.1.31.1.1.1.8.11 = Counter64: 0
.*******.2.1.31.1.1.1.8.12 = Counter64: 0
.*******.2.1.31.1.1.1.8.13 = Counter64: 147
.*******.2.1.31.1.1.1.8.14 = Counter64: 147
.*******.2.1.31.1.1.1.9.1 = Counter64: 0
.*******.2.1.31.1.1.1.9.2 = Counter64: 0
.*******.2.1.31.1.1.1.9.3 = Counter64: 0
.*******.2.1.31.1.1.1.9.4 = Counter64: 0
.*******.2.1.31.1.1.1.9.5 = Counter64: 0
.*******.2.1.31.1.1.1.9.6 = Counter64: 0
.*******.2.1.31.1.1.1.9.7 = Counter64: 0
.*******.2.1.31.1.1.1.9.8 = Counter64: 0
.*******.2.1.31.1.1.1.9.9 = Counter64: 0
.*******.2.1.31.1.1.1.9.10 = Counter64: 0
.*******.2.1.31.1.1.1.9.11 = Counter64: 0
.*******.2.1.31.1.1.1.9.12 = Counter64: 0
.*******.2.1.31.1.1.1.9.13 = Counter64: 0
.*******.2.1.31.1.1.1.9.14 = Counter64: 0
.*******.2.1.31.1.1.1.10.1 = Counter64: 4928
.*******.2.1.31.1.1.1.10.2 = Counter64: 28657547
.*******.2.1.31.1.1.1.10.3 = Counter64: 3512529
.*******.2.1.31.1.1.1.10.4 = Counter64: 3512100
.*******.2.1.31.1.1.1.10.5 = Counter64: 3512400
.*******.2.1.31.1.1.1.10.6 = Counter64: 3511920
.*******.2.1.31.1.1.1.10.7 = Counter64: 0
.*******.2.1.31.1.1.1.10.8 = Counter64: 0
.*******.2.1.31.1.1.1.10.9 = Counter64: 0
.*******.2.1.31.1.1.1.10.10 = Counter64: 12072756
.*******.2.1.31.1.1.1.10.11 = Counter64: 0
.*******.2.1.31.1.1.1.10.12 = Counter64: 0
.*******.2.1.31.1.1.1.10.13 = Counter64: 8561688
.*******.2.1.31.1.1.1.10.14 = Counter64: 8560872
.*******.2.1.31.1.1.1.11.1 = Counter64: 64
.*******.2.1.31.1.1.1.11.2 = Counter64: 301463
.*******.2.1.31.1.1.1.11.3 = Counter64: 51976
.*******.2.1.31.1.1.1.11.4 = Counter64: 51971
.*******.2.1.31.1.1.1.11.5 = Counter64: 51975
.*******.2.1.31.1.1.1.11.6 = Counter64: 51971
.*******.2.1.31.1.1.1.11.7 = Counter64: 0
.*******.2.1.31.1.1.1.11.8 = Counter64: 0
.*******.2.1.31.1.1.1.11.9 = Counter64: 0
.*******.2.1.31.1.1.1.11.10 = Counter64: 93563
.*******.2.1.31.1.1.1.11.11 = Counter64: 0
.*******.2.1.31.1.1.1.11.12 = Counter64: 0
.*******.2.1.31.1.1.1.11.13 = Counter64: 41600
.*******.2.1.31.1.1.1.11.14 = Counter64: 41592
.*******.2.1.31.1.1.1.12.1 = Counter64: 0
.*******.2.1.31.1.1.1.12.2 = Counter64: 0
.*******.2.1.31.1.1.1.12.3 = Counter64: 0
.*******.2.1.31.1.1.1.12.4 = Counter64: 0
.*******.2.1.31.1.1.1.12.5 = Counter64: 0
.*******.2.1.31.1.1.1.12.6 = Counter64: 0
.*******.2.1.31.1.1.1.12.7 = Counter64: 0
.*******.2.1.31.1.1.1.12.8 = Counter64: 0
.*******.2.1.31.1.1.1.12.9 = Counter64: 0
.*******.2.1.31.1.1.1.12.10 = Counter64: 0
.*******.2.1.31.1.1.1.12.11 = Counter64: 0
.*******.2.1.31.1.1.1.12.12 = Counter64: 0
.*******.2.1.31.1.1.1.12.13 = Counter64: 0
.*******.2.1.31.1.1.1.12.14 = Counter64: 0
.*******.2.1.31.1.1.1.13.1 = Counter64: 0
.*******.2.1.31.1.1.1.13.2 = Counter64: 0
.*******.2.1.31.1.1.1.13.3 = Counter64: 0
.*******.2.1.31.1.1.1.13.4 = Counter64: 0
.*******.2.1.31.1.1.1.13.5 = Counter64: 0
.*******.2.1.31.1.1.1.13.6 = Counter64: 0
.*******.2.1.31.1.1.1.13.7 = Counter64: 0
.*******.2.1.31.1.1.1.13.8 = Counter64: 0
.*******.2.1.31.1.1.1.13.9 = Counter64: 0
.*******.2.1.31.1.1.1.13.10 = Counter64: 0
.*******.2.1.31.1.1.1.13.11 = Counter64: 0
.*******.2.1.31.1.1.1.13.12 = Counter64: 0
.*******.2.1.31.1.1.1.13.13 = Counter64: 0
.*******.2.1.31.1.1.1.13.14 = Counter64: 0
.*******.2.1.31.1.1.1.15.1 = Gauge32: 10
.*******.2.1.31.1.1.1.15.2 = Gauge32: 1000
.*******.2.1.31.1.1.1.15.3 = Gauge32: 100
.*******.2.1.31.1.1.1.15.4 = Gauge32: 100
.*******.2.1.31.1.1.1.15.5 = Gauge32: 1000
.*******.2.1.31.1.1.1.15.6 = Gauge32: 1000
.*******.2.1.31.1.1.1.15.7 = Gauge32: 0
.*******.2.1.31.1.1.1.15.8 = Gauge32: 0
.*******.2.1.31.1.1.1.15.9 = Gauge32: 0
.*******.2.1.31.1.1.1.15.10 = Gauge32: 100
.*******.2.1.31.1.1.1.15.11 = Gauge32: 1000
.*******.2.1.31.1.1.1.15.12 = Gauge32: 1000
.*******.2.1.31.1.1.1.15.13 = Gauge32: 1000
.*******.2.1.31.1.1.1.15.14 = Gauge32: 1000
.*******.2.1.31.1.1.1.16.1 = INTEGER: 2
.*******.2.1.31.1.1.1.16.2 = INTEGER: 2
.*******.2.1.31.1.1.1.16.3 = INTEGER: 2
.*******.2.1.31.1.1.1.16.4 = INTEGER: 2
.*******.2.1.31.1.1.1.16.5 = INTEGER: 2
.*******.2.1.31.1.1.1.16.6 = INTEGER: 2
.*******.2.1.31.1.1.1.16.7 = INTEGER: 2
.*******.2.1.31.1.1.1.16.8 = INTEGER: 2
.*******.2.1.31.1.1.1.16.9 = INTEGER: 2
.*******.2.1.31.1.1.1.16.10 = INTEGER: 2
.*******.2.1.31.1.1.1.16.11 = INTEGER: 2
.*******.2.1.31.1.1.1.16.12 = INTEGER: 2
.*******.2.1.31.1.1.1.16.13 = INTEGER: 2
.*******.2.1.31.1.1.1.16.14 = INTEGER: 2
.*******.2.1.31.1.1.1.17.1 = INTEGER: 2
.*******.2.1.31.1.1.1.17.2 = INTEGER: 1
.*******.2.1.31.1.1.1.17.3 = INTEGER: 1
.*******.2.1.31.1.1.1.17.4 = INTEGER: 1
.*******.2.1.31.1.1.1.17.5 = INTEGER: 1
.*******.2.1.31.1.1.1.17.6 = INTEGER: 1
.*******.2.1.31.1.1.1.17.7 = INTEGER: 1
.*******.2.1.31.1.1.1.17.8 = INTEGER: 1
.*******.2.1.31.1.1.1.17.9 = INTEGER: 1
.*******.2.1.31.1.1.1.17.10 = INTEGER: 1
.*******.2.1.31.1.1.1.17.11 = INTEGER: 1
.*******.2.1.31.1.1.1.17.12 = INTEGER: 1
.*******.2.1.31.1.1.1.17.13 = INTEGER: 1
.*******.2.1.31.1.1.1.17.14 = INTEGER: 1
.*******.2.1.31.1.1.1.18.1 = ""
.*******.2.1.31.1.1.1.18.2 = ""
.*******.2.1.31.1.1.1.18.3 = ""
.*******.2.1.31.1.1.1.18.4 = ""
.*******.2.1.31.1.1.1.18.5 = ""
.*******.2.1.31.1.1.1.18.6 = ""
.*******.2.1.31.1.1.1.18.7 = ""
.*******.2.1.31.1.1.1.18.8 = ""
.*******.2.1.31.1.1.1.18.9 = ""
.*******.2.1.31.1.1.1.18.10 = ""
.*******.2.1.31.1.1.1.18.11 = ""
.*******.2.1.31.1.1.1.18.12 = ""
.*******.2.1.31.1.1.1.18.13 = ""
.*******.2.1.31.1.1.1.18.14 = ""
.*******.2.1.31.1.1.1.19.1 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.2 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.3 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.4 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.5 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.6 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.7 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.8 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.9 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.10 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.11 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.12 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.13 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.1.1.19.14 = Timeticks: (0) 0:00:00.00
.*******.2.1.31.1.5.0 = Timeticks: (0) 0:00:00.00
.*******.4.1.2021.4.1.0 = INTEGER: 0
.*******.4.1.2021.4.2.0 = STRING: "swap"
.*******.4.1.2021.4.3.0 = INTEGER: 0
.*******.4.1.2021.4.4.0 = INTEGER: 0
.*******.4.1.2021.4.5.0 = INTEGER: 124128
.*******.4.1.2021.4.6.0 = INTEGER: 71988
.*******.4.1.2021.4.11.0 = INTEGER: 71988
.*******.4.1.2021.4.12.0 = INTEGER: 16000
.*******.4.1.2021.4.13.0 = INTEGER: 88
.*******.4.1.2021.4.14.0 = INTEGER: 0
.*******.4.1.2021.4.15.0 = INTEGER: 22256
.*******.4.1.2021.4.18.0 = Counter64: 0
.*******.4.1.2021.4.19.0 = Counter64: 0
.*******.4.1.2021.4.20.0 = Counter64: 124128
.*******.4.1.2021.4.21.0 = Counter64: 71988
.*******.4.1.2021.4.22.0 = Counter64: 71988
.*******.4.1.2021.4.23.0 = Counter64: 16000
.*******.4.1.2021.4.24.0 = Counter64: 88
.*******.4.1.2021.4.25.0 = Counter64: 0
.*******.4.1.2021.4.26.0 = Counter64: 22256
.*******.4.1.2021.4.27.0 = Counter64: 58508
.*******.4.1.2021.4.100.0 = INTEGER: 1
.*******.4.1.2021.4.101.0 = STRING: "Running out of swap space (0)"
.*******.4.1.2021.******* = INTEGER: 1
.*******.4.1.2021.******* = STRING: "filedescriptors"
.*******.4.1.2021.******* = STRING: "/bin/cat /proc/sys/fs/file-nr"
.*******.4.1.2021.********* = INTEGER: 0
.*******.4.1.2021.********* = STRING: "305 0 12279"
.*******.4.1.2021.********* = INTEGER: 0
.*******.4.1.2021.********* = ""
.*******.4.1.2021.******** = INTEGER: 1
.*******.4.1.2021.******** = INTEGER: 2
.*******.4.1.2021.******** = INTEGER: 3
.*******.4.1.2021.******** = STRING: "Load-1"
.*******.4.1.2021.******** = STRING: "Load-5"
.*******.4.1.2021.******** = STRING: "Load-15"
.*******.4.1.2021.******** = STRING: "1.02"
.*******.4.1.2021.******** = STRING: "1.03"
.*******.4.1.2021.******** = STRING: "1.00"
.*******.4.1.2021.******** = STRING: "12.00"
.*******.4.1.2021.******** = STRING: "12.00"
.*******.4.1.2021.******** = STRING: "12.00"
.*******.4.1.2021.******** = INTEGER: 102
.*******.4.1.2021.******** = INTEGER: 102
.*******.4.1.2021.******** = INTEGER: 100
.*******.4.1.2021.******** = Opaque: Float: 1.020508
.*******.4.1.2021.******** = Opaque: Float: 1.027832
.*******.4.1.2021.10.1.6.3 = Opaque: Float: 1.000000
.*******.4.1.2021.10.1.100.1 = INTEGER: 0
.*******.4.1.2021.10.1.100.2 = INTEGER: 0
.*******.4.1.2021.10.1.100.3 = INTEGER: 0
.*******.4.1.2021.10.1.101.1 = ""
.*******.4.1.2021.10.1.101.2 = ""
.*******.4.1.2021.10.1.101.3 = ""
.*******.4.1.2021.11.1.0 = INTEGER: 1
.*******.4.1.2021.11.2.0 = STRING: "systemStats"
.*******.4.1.2021.11.3.0 = INTEGER: 0
.*******.4.1.2021.11.4.0 = INTEGER: 0
.*******.4.1.2021.11.5.0 = INTEGER: 0
.*******.4.1.2021.11.6.0 = INTEGER: 0
.*******.4.1.2021.11.7.0 = INTEGER: 104
.*******.4.1.2021.11.8.0 = INTEGER: 230
.*******.4.1.2021.11.9.0 = INTEGER: 2
.*******.4.1.2021.11.10.0 = INTEGER: 3
.*******.4.1.2021.11.11.0 = INTEGER: 93
.*******.4.1.2021.11.50.0 = Counter32: 98359
.*******.4.1.2021.11.51.0 = Counter32: 0
.*******.4.1.2021.11.52.0 = Counter32: 264210
.*******.4.1.2021.11.53.0 = Counter32: 7061754
.*******.4.1.2021.11.54.0 = Counter32: 0
.*******.4.1.2021.11.55.0 = Counter32: 0
.*******.4.1.2021.11.56.0 = Counter32: 0
.*******.4.1.2021.11.57.0 = Counter32: 0
.*******.4.1.2021.11.58.0 = Counter32: 0
.*******.4.1.2021.11.59.0 = Counter32: 8430827
.*******.4.1.2021.11.60.0 = Counter32: 18485894
.*******.4.1.2021.11.61.0 = Counter32: 2114
.*******.4.1.2021.11.62.0 = Counter32: 0
.*******.4.1.2021.11.63.0 = Counter32: 0
.*******.4.1.2021.11.64.0 = Counter32: 0
.*******.4.1.2021.11.65.0 = Counter32: 0
.*******.4.1.2021.11.66.0 = Counter32: 0
.*******.4.1.2021.11.67.0 = INTEGER: 1
.*******.4.1.2021.13.14.1.0 = INTEGER: 4
.*******.4.1.2021.13.14.2.1.2.1 = STRING: "device"
.*******.4.1.2021.13.14.******* = STRING: "iface"
.*******.4.1.2021.13.14.2.1.2.3 = STRING: "vlan"
.*******.4.1.2021.13.14.******* = STRING: "/usr/lib/snmpd-mod/device.so"
.*******.4.1.2021.13.14.2.1.3.2 = STRING: "/usr/lib/snmpd-mod/iface.so"
.*******.4.1.2021.13.14.2.1.3.3 = STRING: "/usr/lib/snmpd-mod/vlan.so"
.*******.4.1.2021.13.14.2.1.5.1 = INTEGER: 1
.*******.4.1.2021.13.14.2.1.5.2 = INTEGER: 1
.*******.4.1.2021.13.14.2.1.5.3 = INTEGER: 1
.*******.4.1.8072.1.3.2.1.0 = INTEGER: 0
.*******.4.1.48690.1.1.0 = STRING: "6001244780"
.*******.4.1.48690.1.2.0 = STRING: "customer-location-switch"
.*******.4.1.48690.1.3.0 = STRING: "TSW21200XXXX"
.*******.4.1.48690.1.4.0 = STRING: "0002"
.*******.4.1.48690.1.5.0 = STRING: "0003"
.*******.4.1.48690.1.6.0 = STRING: "TSW2_R_00.01.02.2"
.*******.4.1.48690.1.7.0 = STRING: "74268"
.*******.4.1.48690.1.8.0 = STRING: "2.00"
.*******.4.1.48690.8.1.0 = INTEGER: 1
.*******.4.1.48690.8.2.1.1.1 = INTEGER: 1
.*******.4.1.48690.8.2.1.2.1 = STRING: "vlan1"
.*******.4.1.48690.8.******* = INTEGER: 1
.*******.4.1.48690.8.******* = STRING: "port1:u port2:u port3:u port4:u port5:u port6:u port7:u port8:u sfp1:u sfp2:u"
.*******.4.1.48690.8.2.1.5.1 = STRING: "None"
.*******.4.1.48690.11.1.0 = INTEGER: 14
.*******.4.1.48690.11.2.1.1.1 = INTEGER: 1
.*******.4.1.48690.11.2.1.1.2 = INTEGER: 2
.*******.4.1.48690.11.2.1.1.3 = INTEGER: 3
.*******.4.1.48690.11.2.1.1.4 = INTEGER: 4
.*******.4.1.48690.11.2.1.1.5 = INTEGER: 5
.*******.4.1.48690.11.2.1.1.6 = INTEGER: 6
.*******.4.1.48690.11.2.1.1.7 = INTEGER: 7
.*******.4.1.48690.11.******* = INTEGER: 8
.*******.4.1.48690.11.******* = INTEGER: 9
.*******.4.1.48690.11.2.1.1.10 = INTEGER: 10
.*******.4.1.48690.11.2.1.1.11 = INTEGER: 11
.*******.4.1.48690.11.2.1.1.12 = INTEGER: 12
.*******.4.1.48690.11.2.1.1.13 = INTEGER: 13
.*******.4.1.48690.11.2.1.1.14 = INTEGER: 14
.*******.4.1.48690.11.2.1.2.1 = STRING: "eth0"
.*******.4.1.48690.11.******* = STRING: "br0.1"
.*******.4.1.48690.11.2.1.2.3 = STRING: "port7"
.*******.4.1.48690.11.2.1.2.4 = STRING: "sfp1"
.*******.4.1.48690.11.2.1.2.5 = STRING: "port5"
.*******.4.1.48690.11.2.1.2.6 = STRING: "br0"
.*******.4.1.48690.11.2.1.2.7 = STRING: "port3"
.*******.4.1.48690.11.2.1.2.8 = STRING: "port1"
.*******.4.1.48690.11.2.1.2.9 = STRING: "port8"
.*******.4.1.48690.11.2.1.2.10 = STRING: "sfp2"
.*******.4.1.48690.11.2.1.2.11 = STRING: "port6"
.*******.4.1.48690.11.2.1.2.12 = STRING: "port4"
.*******.4.1.48690.11.2.1.2.13 = STRING: "port2"
.*******.4.1.48690.11.2.1.2.14 = STRING: "lo"
.*******.4.1.48690.11.******* = STRING: "up"
.*******.4.1.48690.11.2.1.3.2 = STRING: "up"
.*******.4.1.48690.11.2.1.3.3 = STRING: "lowerlayerdown"
.*******.4.1.48690.11.2.1.3.4 = STRING: "lowerlayerdown"
.*******.4.1.48690.11.2.1.3.5 = STRING: "lowerlayerdown"
.*******.4.1.48690.11.2.1.3.6 = STRING: "up"
.*******.4.1.48690.11.2.1.3.7 = STRING: "up"
.*******.4.1.48690.11.2.1.3.8 = STRING: "up"
.*******.4.1.48690.11.2.1.3.9 = STRING: "up"
.*******.4.1.48690.11.*******0 = STRING: "lowerlayerdown"
.*******.4.1.48690.11.*******1 = STRING: "lowerlayerdown"
.*******.4.1.48690.11.*******2 = STRING: "up"
.*******.4.1.48690.11.*******3 = STRING: "up"
.*******.4.1.48690.11.*******4 = STRING: "unknown"
.*******.4.1.48690.1*******.1 = INTEGER: 1000
.*******.4.1.48690.1*******.2 = INTEGER: 1000
.*******.4.1.48690.1*******.3 = INTEGER: -1
.*******.4.1.48690.1*******.4 = INTEGER: 1000
.*******.4.1.48690.1*******.5 = INTEGER: -1
.*******.4.1.48690.1*******.6 = INTEGER: 1000
.*******.4.1.48690.1*******.7 = INTEGER: 1000
.*******.4.1.48690.1*******.8 = INTEGER: 100
.*******.4.1.48690.1*******.9 = INTEGER: 100
.*******.4.1.48690.1*******.10 = INTEGER: 1000
.*******.4.1.48690.1*******.11 = INTEGER: -1
.*******.4.1.48690.1*******.12 = INTEGER: 1000
.*******.4.1.48690.1*******.13 = INTEGER: 100
.*******.4.1.48690.1*******.14 = INTEGER: 0
.*******.4.1.48690.11.2.1.5.1 = INTEGER: 0
.*******.4.1.48690.11.2.1.5.2 = INTEGER: 147
.*******.4.1.48690.11.2.1.5.3 = INTEGER: 0
.*******.4.1.48690.11.2.1.5.4 = INTEGER: 0
.*******.4.1.48690.11.2.1.5.5 = INTEGER: 0
.*******.4.1.48690.11.2.1.5.6 = INTEGER: 147
.*******.4.1.48690.11.2.1.5.7 = INTEGER: 0
.*******.4.1.48690.11.2.1.5.8 = INTEGER: 0
.*******.4.1.48690.11.2.1.5.9 = INTEGER: 0
.*******.4.1.48690.11.2.1.5.10 = INTEGER: 0
.*******.4.1.48690.11.2.1.5.11 = INTEGER: 0
.*******.4.1.48690.11.2.1.5.12 = INTEGER: 0
.*******.4.1.48690.11.2.1.5.13 = INTEGER: 0
.*******.4.1.48690.11.2.1.5.14 = INTEGER: 0
.*******.4.1.48690.1*******.1 = INTEGER: 0
.*******.4.1.48690.1*******.2 = INTEGER: 0
.*******.4.1.48690.1*******.3 = INTEGER: 0
.*******.4.1.48690.1*******.4 = INTEGER: 0
.*******.4.1.48690.1*******.5 = INTEGER: 0
.*******.4.1.48690.1*******.6 = INTEGER: 0
.*******.4.1.48690.1*******.7 = INTEGER: 0
.*******.4.1.48690.11.******* = INTEGER: 0
.*******.4.1.48690.11.******* = INTEGER: 0
.*******.4.1.48690.11.******** = INTEGER: 0
.*******.4.1.48690.11.******** = INTEGER: 0
.*******.4.1.48690.11.******** = INTEGER: 0
.*******.4.1.48690.11.******** = INTEGER: 0
.*******.4.1.48690.1*******.14 = INTEGER: 0
.*******.4.1.48690.11.******* = INTEGER: 0
.*******.4.1.48690.11.******* = INTEGER: 0
.*******.4.1.48690.11.******* = INTEGER: 0
.*******.4.1.48690.11.2.1.7.4 = INTEGER: 0
.*******.4.1.48690.11.2.1.7.5 = INTEGER: 0
.*******.4.1.48690.11.2.1.7.6 = INTEGER: 0
.*******.4.1.48690.11.2.1.7.7 = INTEGER: 0
.*******.4.1.48690.11.2.1.7.8 = INTEGER: 0
.*******.4.1.48690.11.2.1.7.9 = INTEGER: 0
.*******.4.1.48690.11.*******0 = INTEGER: 0
.*******.4.1.48690.11.*******1 = INTEGER: 0
.*******.4.1.48690.11.*******2 = INTEGER: 0
.*******.4.1.48690.11.*******3 = INTEGER: 0
.*******.4.1.48690.11.*******4 = INTEGER: 0
.*******.4.1.48690.11.2.1.8.1 = INTEGER: 4452336
.*******.4.1.48690.11.2.1.8.2 = INTEGER: 3181480
.*******.4.1.48690.11.2.1.8.3 = INTEGER: 0
.*******.4.1.48690.11.2.1.8.4 = INTEGER: 0
.*******.4.1.48690.11.2.1.8.5 = INTEGER: 0
.*******.4.1.48690.11.2.1.8.6 = INTEGER: 3181480
.*******.4.1.48690.11.2.1.8.7 = INTEGER: 2932
.*******.4.1.48690.11.2.1.8.8 = INTEGER: 2708
.*******.4.1.48690.11.2.1.8.9 = INTEGER: 3533984
.*******.4.1.48690.11.2.1.8.10 = INTEGER: 0
.*******.4.1.48690.11.2.1.8.11 = INTEGER: 0
.*******.4.1.48690.11.2.1.8.12 = INTEGER: 16930
.*******.4.1.48690.11.2.1.8.13 = INTEGER: 3904
.*******.4.1.48690.11.2.1.8.14 = INTEGER: 4928
.*******.4.1.48690.11.2.1.9.1 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.2 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.3 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.4 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.5 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.6 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.7 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.8 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.9 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.10 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.11 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.12 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.13 = INTEGER: 0
.*******.4.1.48690.11.2.1.9.14 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.1 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.2 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.3 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.4 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.5 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.6 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.7 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.8 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.9 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.10 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.11 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.12 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.13 = INTEGER: 0
.*******.4.1.48690.11.2.1.10.14 = INTEGER: 0
.*******.4.1.48690.11.2.1.11.1 = INTEGER: 28676022
.*******.4.1.48690.11.2.1.11.2 = INTEGER: 8576615
.*******.4.1.48690.11.2.1.11.3 = INTEGER: 0
.*******.4.1.48690.11.2.1.11.4 = INTEGER: 0
.*******.4.1.48690.11.2.1.11.5 = INTEGER: 0
.*******.4.1.48690.11.2.1.11.6 = INTEGER: 8577711
.*******.4.1.48690.11.2.1.11.7 = INTEGER: 3512873
.*******.4.1.48690.11.2.1.11.8 = INTEGER: 3512949
.*******.4.1.48690.11.2.1.11.9 = INTEGER: 12089148
.*******.4.1.48690.11.2.1.11.10 = INTEGER: 0
.*******.4.1.48690.11.2.1.11.11 = INTEGER: 0
.*******.4.1.48690.11.2.1.11.12 = INTEGER: 3512393
.*******.4.1.48690.11.2.1.11.13 = INTEGER: 3512573
.*******.4.1.48690.11.2.1.11.14 = INTEGER: 4928
.*******.6.3.10.2.1.1.0 = Hex-STRING: 80 00 1F 88 80 11 8A C8 FD 00 00 00 00 66 A6 C2 1E
.*******.6.3.10.2.1.2.0 = INTEGER: 1
.*******.6.3.10.2.1.3.0 = INTEGER: 74203
.*******.6.3.10.2.1.4.0 = INTEGER: 1500
.*******.6.3.11.2.1.1.0 = Counter32: 0
.*******.6.3.11.2.1.2.0 = Counter32: 0
.*******.6.3.11.2.1.3.0 = Counter32: 0
.*******.6.3.15.1.1.1.0 = Counter32: 0
.*******.6.3.15.1.1.2.0 = Counter32: 0
.*******.6.3.15.1.1.3.0 = Counter32: 0
.*******.6.3.15.1.1.4.0 = Counter32: 0
.*******.6.3.15.1.1.5.0 = Counter32: 0
.*******.6.3.15.1.1.6.0 = Counter32: 0
.*******.6.3.15.1.2.1.0 = INTEGER: 0
.*******.6.3.16.1.1.1.1.0 = ""
.*******.6.3.16.1.2.1.3.2.2.114.111 = STRING: "public"
.*******.6.3.16.1.2.1.3.2.2.114.119 = STRING: "private"
.*******.6.3.16.1.2.1.3.3.2.114.111 = STRING: "public"
.*******.6.3.16.1.2.1.3.3.2.114.119 = STRING: "private"
.*******.6.3.16.*******.2.2.114.111 = INTEGER: 4
.*******.6.3.16.*******.2.2.114.119 = INTEGER: 4
.*******.6.3.16.*******.3.2.114.111 = INTEGER: 4
.*******.6.3.16.*******.3.2.114.119 = INTEGER: 4
.*******.6.3.16.1.2.1.5.2.2.114.111 = INTEGER: 1
.*******.6.3.16.1.2.1.5.2.2.114.119 = INTEGER: 1
.*******.6.3.16.1.2.1.5.3.2.114.111 = INTEGER: 1
.*******.6.3.16.1.2.1.5.3.2.114.119 = INTEGER: 1
.*******.6.3.16.1.4.1.4.6.112.117.98.108.105.99.0.0.1 = INTEGER: 1
.*******.6.3.16.1.4.1.4.7.112.114.105.118.97.116.101.0.0.1 = INTEGER: 1
.*******.6.3.16.1.4.1.5.6.112.117.98.108.105.99.0.0.1 = STRING: "all"
.*******.6.3.16.1.4.1.5.7.112.114.105.118.97.116.101.0.0.1 = STRING: "all"
.*******.6.3.16.1.4.1.6.6.112.117.98.108.105.99.0.0.1 = STRING: "none"
.*******.6.3.16.1.4.1.6.7.112.114.105.118.97.116.101.0.0.1 = STRING: "all"
.*******.6.3.16.1.4.1.7.6.112.117.98.108.105.99.0.0.1 = STRING: "none"
.*******.6.3.16.1.4.1.7.7.112.114.105.118.97.116.101.0.0.1 = STRING: "all"
.*******.6.3.16.1.4.1.8.6.112.117.98.108.105.99.0.0.1 = INTEGER: 4
.*******.6.3.16.1.4.1.8.7.112.114.105.118.97.116.101.0.0.1 = INTEGER: 4
.*******.6.3.16.1.4.1.9.6.112.117.98.108.105.99.0.0.1 = INTEGER: 1
.*******.6.3.16.1.4.1.9.7.112.114.105.118.97.116.101.0.0.1 = INTEGER: 1
.*******.6.3.16.1.5.1.0 = INTEGER: 0
.*******.6.3.16.1.5.2.1.3.3.97.108.108.1.1 = ""
.*******.6.3.16.1.5.2.1.3.5.95.97.108.108.95.1.0 = ""
.*******.6.3.16.1.5.2.1.3.5.95.97.108.108.95.1.1 = ""
.*******.6.3.16.1.5.2.1.3.5.95.97.108.108.95.1.2 = ""
.*******.6.3.16.1.5.2.1.3.6.95.110.111.110.101.95.1.0 = ""
.*******.6.3.16.1.5.2.1.3.6.95.110.111.110.101.95.1.1 = ""
.*******.6.3.16.1.5.2.1.3.6.95.110.111.110.101.95.1.2 = ""
.*******.6.3.16.1.5.*******.97.108.108.1.1 = INTEGER: 1
.*******.6.3.16.1.5.*******.95.97.108.108.95.1.0 = INTEGER: 1
.*******.6.3.16.1.5.*******.95.97.108.108.95.1.1 = INTEGER: 1
.*******.6.3.16.1.5.*******.95.97.108.108.95.1.2 = INTEGER: 1
.*******.6.3.16.1.5.*******.95.110.111.110.101.95.1.0 = INTEGER: 2
.*******.6.3.16.1.5.*******.95.110.111.110.101.95.1.1 = INTEGER: 2
.*******.6.3.16.1.5.*******.95.110.111.110.101.95.1.2 = INTEGER: 2
.*******.6.3.16.1.5.2.1.5.3.97.108.108.1.1 = INTEGER: 4
.*******.6.3.16.1.5.2.1.5.5.95.97.108.108.95.1.0 = INTEGER: 4
.*******.6.3.16.1.5.2.1.5.5.95.97.108.108.95.1.1 = INTEGER: 4
.*******.6.3.16.1.5.2.1.5.5.95.97.108.108.95.1.2 = INTEGER: 4
.*******.6.3.16.1.5.2.1.5.6.95.110.111.110.101.95.1.0 = INTEGER: 4
.*******.6.3.16.1.5.2.1.5.6.95.110.111.110.101.95.1.1 = INTEGER: 4
.*******.6.3.16.1.5.2.1.5.6.95.110.111.110.101.95.1.2 = INTEGER: 4
.*******.6.3.16.1.5.2.1.6.3.97.108.108.1.1 = INTEGER: 1
.*******.6.3.16.1.5.*******.95.97.108.108.95.1.0 = INTEGER: 1
.*******.6.3.16.1.5.*******.95.97.108.108.95.1.1 = INTEGER: 1
.*******.6.3.16.1.5.*******.95.97.108.108.95.1.2 = INTEGER: 1
.*******.6.3.16.1.5.*******.95.110.111.110.101.95.1.0 = INTEGER: 1
.*******.6.3.16.1.5.*******.95.110.111.110.101.95.1.1 = INTEGER: 1
.*******.6.3.16.1.5.*******.95.110.111.110.101.95.1.2 = INTEGER: 1
.*******.6.3.16.1.5.*******.95.110.111.110.101.95.1.2 = No more variables left in this MIB View (It is past the end of the MIB tree)
