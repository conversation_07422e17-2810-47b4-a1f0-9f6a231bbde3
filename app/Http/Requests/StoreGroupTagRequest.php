<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreGroupTagRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'device_tags' => 'required|array',
            'device_tag_user_role' => 'nullable|numeric|in:0,1',
            'device_id' => 'required|numeric|exists:devices,device_id',
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
