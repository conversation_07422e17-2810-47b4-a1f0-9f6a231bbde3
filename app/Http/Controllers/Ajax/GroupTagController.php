<?php

namespace App\Http\Controllers\Ajax;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreGroupTagRequest;
use App\Models\ArraGroupTag;
use App\Models\Device;
use Illuminate\Http\Request;

class GroupTagController extends Controller
{
    public function store(StoreGroupTagRequest $request)
    {
        $validated = $request->validated();

        $device = Device::hasAccess(\Auth::user())->with(['groups'])->find($validated['device_id']);
        if ($device === null) {
            return response()->json(['message' => 'Device not found'], 404);
        }
        if ($device->groups->isEmpty()) {
            $groupId = null;
        } else {
            $groupId = $device->groups->first()->id;
        }

        foreach ($validated['device_tags'] as $key => $tag) {
            $validated['device_tags'][$key] = trim(strtolower($tag));
        }

        $query = ArraGroupTag::where('device_id', $validated['device_id']);
        if ($groupId !== null) {
            $query->where('device_group_id', $groupId);
        }

        if (\Auth::user()->isAdmin()) {
            $query->where('added_as_admin', $validated['device_tag_user_role']);
        } else {
            $query->where('added_as_admin', 0);
        }
        $alreadyExistingTags = $query->get();

        $selectedTags = $validated['device_tags'];

        $alreadyExistingTags->each(function ($tag) use (&$selectedTags) {
            $keys = array_keys($selectedTags, $tag->label);
            foreach ($keys as $key) {
                unset($selectedTags[$key]);
            }
        });

        foreach ($selectedTags as $tag) {
            ArraGroupTag::create([
                'device_id' => $validated['device_id'],
                'device_group_id' => $groupId,
                'label' => $tag,
                'added_as_admin' => $validated['device_tag_user_role'],
            ]);
        }


        return response()->json(['message' => 'Tags saved']);
    }

    public function destroy(Request $request)
    {
        if (!$request->has('tag_id') || empty($request->tag_id)) {
            return response()->json(['message' => 'Invalid request'], 400);
        }

        $tag = ArraGroupTag::find($request->tag_id);
        $tag->delete();

        return response()->json(['message' => 'Tag deleted']);
    }
}
