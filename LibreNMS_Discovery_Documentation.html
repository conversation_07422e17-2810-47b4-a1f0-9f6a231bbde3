<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LibreNMS Discovery - Complete Documentation</title>
</head>
<body>

<h1>LibreNMS Discovery - Complete Documentation</h1>

<h2>Overview</h2>
<p><strong>Discovery in LibreNMS</strong> is the process by which the system identifies and collects information about network devices. This process runs automatically at regular intervals to keep device information up to date.</p>

<h2>Discovery Architecture</h2>

<h3>Discovery Flow in Docker Setup</h3>
<p>
<strong>Dispatcher Service</strong> (librenms_dispatcher)<br>
↓<br>
<strong>DiscoveryQueueManager</strong><br>
↓<br>
<strong>discovery.php</strong> (for each device)<br>
↓<br>
<strong>Logs in volumes/librenms/logs/</strong>
</p>

<h2>Discovery Configuration</h2>

<h3>1. Scheduler Types</h3>

<table border="1">
<tr>
<th>Type</th>
<th>Description</th>
<th>Usage</th>
</tr>
<tr>
<td><code>legacy</code></td>
<td>Uses dispatcher service or cron</td>
<td>Current setup (recommended)</td>
</tr>
<tr>
<td><code>cron</code></td>
<td>Uses only cron jobs</td>
<td>Simple setups</td>
</tr>
<tr>
<td><code>dispatcher</code></td>
<td>Uses only dispatcher service</td>
<td>Advanced setups</td>
</tr>
</table>

<h3>2. Main Configuration Settings</h3>

<h4>Check Current Configuration</h4>
<pre>
# Check current configuration
docker exec librenms php artisan config:get schedule_type.discovery
# Result: legacy

docker exec librenms php artisan config:get service_discovery_frequency
# Result: 21600 (6 hours in seconds)

docker exec librenms php artisan config:get service_discovery_workers
# Result: number of workers
</pre>

<h3>3. Modifying Configuration</h3>

<h4>Configuration Changes</h4>
<pre>
# Change frequency (in seconds)
docker exec librenms php artisan config:set service_discovery_frequency 10800  # 3 hours

# Change number of workers
docker exec librenms php artisan config:set service_discovery_workers 4

# Change scheduler type
docker exec librenms php artisan config:set schedule_type.discovery dispatcher
</pre>

<h2>Docker Containers Involved</h2>

<table border="1">
<tr>
<th>Container</th>
<th>Role</th>
<th>Important Files</th>
</tr>
<tr>
<td><strong>librenms_dispatcher</strong></td>
<td>Executes discovery through dispatcher service</td>
<td>/opt/librenms/librenms-service.py</td>
</tr>
<tr>
<td><strong>librenms_cron</strong></td>
<td>Runs scheduled tasks (Laravel scheduler)</td>
<td>/var/spool/cron/crontabs/librenms</td>
</tr>
<tr>
<td><strong>librenms</strong></td>
<td>Main application container</td>
<td>/opt/librenms/discovery.php</td>
</tr>
</table>

<h2>Execution Interval</h2>

<p><strong>Default Interval:</strong> Discovery runs every <strong>6 hours (21600 seconds)</strong> by default</p>

<h3>How the Interval is Determined</h3>
<ol>
<li><strong>Default configuration:</strong> 21600 seconds (6 hours) in <code>LibreNMS/service.py</code></li>
<li><strong>Custom configuration:</strong> <code>service_discovery_frequency</code></li>
<li><strong>Per node:</strong> Can be overridden for each individual node</li>
</ol>

<h2>Important Files and Locations</h2>

<table border="1">
<tr>
<th>File/Directory</th>
<th>Description</th>
<th>Docker Location</th>
</tr>
<tr>
<td><code>discovery.php</code></td>
<td>Main discovery script</td>
<td>/opt/librenms/discovery.php</td>
</tr>
<tr>
<td><code>LibreNMS/service.py</code></td>
<td>Dispatcher service</td>
<td>/opt/librenms/LibreNMS/service.py</td>
</tr>
<tr>
<td><code>LibreNMS/queuemanager.py</code></td>
<td>Discovery queue manager</td>
<td>/opt/librenms/LibreNMS/queuemanager.py</td>
</tr>
<tr>
<td><code>volumes/librenms/logs/</code></td>
<td>Discovery logs</td>
<td>Host: volumes/librenms/logs/</td>
</tr>
</table>

<h2>Discovery Monitoring</h2>

<h3>1. Checking Logs</h3>

<h4>Log Monitoring Commands</h4>
<pre>
# Recent discovery logs
grep "devices discovered in" volumes/librenms/logs/librenms.log | tail -10

# Real-time monitoring
docker exec librenms tail -f /opt/librenms/logs/librenms.log | grep discovery
</pre>

<h3>2. Service Status Check</h3>

<h4>Service Status Commands</h4>
<pre>
# Container status
docker ps | grep librenms

# Dispatcher processes
docker exec librenms_dispatcher ps aux | grep python

# Laravel scheduler check
docker exec librenms php artisan schedule:list
</pre>

<h3>3. Typical Log Messages</h3>

<h4>Log Message Examples</h4>
<pre>
# Successful discovery
/opt/librenms/discovery.php 1 2025-05-30 07:12:02 - 0 devices discovered in 1.027 secs

# Discovery with errors
worker 1 finished device 123 in 45 seconds with exit code 1
</pre>

<h2>Manual Execution</h2>

<h3>Discovery for All Devices</h3>
<pre>
docker exec librenms php /opt/librenms/discovery.php -h all
</pre>

<h3>Discovery for Specific Device</h3>
<pre>
docker exec librenms php /opt/librenms/discovery.php -h 1
</pre>

<h3>Discovery with Debug</h3>
<pre>
docker exec librenms php /opt/librenms/discovery.php -h 1 -d
</pre>

<h3>Discovery for Specific Modules</h3>
<pre>
docker exec librenms php /opt/librenms/discovery.php -h 1 -m ports,sensors
</pre>

<h2>Troubleshooting</h2>

<h3>Common Issues</h3>
<ul>
<li><strong>Discovery not running:</strong> Check if dispatcher service is active</li>
<li><strong>Missing logs:</strong> Check permissions on volumes/ directory</li>
<li><strong>Poor performance:</strong> Increase number of workers</li>
<li><strong>Timeouts:</strong> Check SNMP connectivity</li>
</ul>

<h3>Diagnostic Commands</h3>

<h4>Troubleshooting Commands</h4>
<pre>
# Check discovery configuration
docker exec librenms php artisan config:get | grep discovery

# Check service status
docker exec librenms_dispatcher python3 -c "import LibreNMS; print('Service OK')"

# Test SNMP connectivity
docker exec librenms php /opt/librenms/snmp-scan.py -h ***********
</pre>

<h2>Performance Optimization</h2>

<table border="1">
<tr>
<th>Parameter</th>
<th>Default Value</th>
<th>Recommendation</th>
</tr>
<tr>
<td>service_discovery_workers</td>
<td>16</td>
<td>2-4 for small setups, 8-16 for large setups</td>
</tr>
<tr>
<td>service_discovery_frequency</td>
<td>21600 (6h)</td>
<td>10800 (3h) for dynamic networks</td>
</tr>
<tr>
<td>discovery timeout</td>
<td>900s</td>
<td>Increase for slow devices</td>
</tr>
</table>

<h3>Performance Tip</h3>
<p>For setups with many devices, consider using distributed polling with memcached for optimal performance.</p>

<hr>

<p><em>Documentation generated for LibreNMS Docker Setup</em></p>

</body>
</html>
