<h1>LibreNMS Discovery - Complete Documentation</h1>

<ac:structured-macro ac:name="info" ac:schema-version="1" ac:macro-id="info-box">
<ac:parameter ac:name="title">Overview</ac:parameter>
<ac:rich-text-body>
<p><strong>Discovery in LibreNMS</strong> is the process by which the system identifies and collects information about network devices. This process runs automatically at regular intervals to keep device information up to date.</p>
</ac:rich-text-body>
</ac:structured-macro>

<h2>Discovery Architecture</h2>

<ac:structured-macro ac:name="panel" ac:schema-version="1" ac:macro-id="flow-diagram">
<ac:parameter ac:name="title">Discovery Flow in Docker Setup</ac:parameter>
<ac:rich-text-body>
<p style="text-align: center;">
<strong>Dispatcher Service</strong> (librenms_dispatcher)<br/>
↓<br/>
<strong>DiscoveryQueueManager</strong><br/>
↓<br/>
<strong>discovery.php</strong> (for each device)<br/>
↓<br/>
<strong>Logs in volumes/librenms/logs/</strong>
</p>
</ac:rich-text-body>
</ac:structured-macro>

<h2>Discovery Configuration</h2>

<h3>1. Scheduler Types</h3>

<table>
<tbody>
<tr>
<th>Type</th>
<th>Description</th>
<th>Usage</th>
</tr>
<tr>
<td><code>legacy</code></td>
<td>Uses dispatcher service or cron</td>
<td>Current setup (recommended)</td>
</tr>
<tr>
<td><code>cron</code></td>
<td>Uses only cron jobs</td>
<td>Simple setups</td>
</tr>
<tr>
<td><code>dispatcher</code></td>
<td>Uses only dispatcher service</td>
<td>Advanced setups</td>
</tr>
</tbody>
</table>

<h3>2. Main Configuration Settings</h3>

<ac:structured-macro ac:name="code" ac:schema-version="1" ac:macro-id="config-check">
<ac:parameter ac:name="language">bash</ac:parameter>
<ac:parameter ac:name="title">Check Current Configuration</ac:parameter>
<ac:plain-text-body><![CDATA[# Check current configuration
docker exec librenms php artisan config:get schedule_type.discovery
# Result: legacy

docker exec librenms php artisan config:get service_discovery_frequency
# Result: 21600 (6 hours in seconds)

docker exec librenms php artisan config:get service_discovery_workers
# Result: number of workers]]></ac:plain-text-body>
</ac:structured-macro>

<h3>3. Modifying Configuration</h3>

<ac:structured-macro ac:name="code" ac:schema-version="1" ac:macro-id="config-modify">
<ac:parameter ac:name="language">bash</ac:parameter>
<ac:parameter ac:name="title">Configuration Changes</ac:parameter>
<ac:plain-text-body><![CDATA[# Change frequency (in seconds)
docker exec librenms php artisan config:set service_discovery_frequency 10800  # 3 hours

# Change number of workers
docker exec librenms php artisan config:set service_discovery_workers 4

# Change scheduler type
docker exec librenms php artisan config:set schedule_type.discovery dispatcher]]></ac:plain-text-body>
</ac:structured-macro>

<h2>Docker Containers Involved</h2>

<table>
<tbody>
<tr>
<th>Container</th>
<th>Role</th>
<th>Important Files</th>
</tr>
<tr>
<td><strong>librenms_dispatcher</strong></td>
<td>Executes discovery through dispatcher service</td>
<td>/opt/librenms/librenms-service.py</td>
</tr>
<tr>
<td><strong>librenms_cron</strong></td>
<td>Runs scheduled tasks (Laravel scheduler)</td>
<td>/var/spool/cron/crontabs/librenms</td>
</tr>
<tr>
<td><strong>librenms</strong></td>
<td>Main application container</td>
<td>/opt/librenms/discovery.php</td>
</tr>
</tbody>
</table>

<h2>Execution Interval</h2>

<ac:structured-macro ac:name="tip" ac:schema-version="1" ac:macro-id="default-interval">
<ac:parameter ac:name="title">Default Interval</ac:parameter>
<ac:rich-text-body>
<p>Discovery runs every <strong>6 hours (21600 seconds)</strong> by default</p>
</ac:rich-text-body>
</ac:structured-macro>

<h3>How the Interval is Determined</h3>
<ol>
<li><strong>Default configuration:</strong> 21600 seconds (6 hours) in <code>LibreNMS/service.py</code></li>
<li><strong>Custom configuration:</strong> <code>service_discovery_frequency</code></li>
<li><strong>Per node:</strong> Can be overridden for each individual node</li>
</ol>

<h2>Important Files and Locations</h2>

<table>
<tbody>
<tr>
<th>File/Directory</th>
<th>Description</th>
<th>Docker Location</th>
</tr>
<tr>
<td><code>discovery.php</code></td>
<td>Main discovery script</td>
<td>/opt/librenms/discovery.php</td>
</tr>
<tr>
<td><code>LibreNMS/service.py</code></td>
<td>Dispatcher service</td>
<td>/opt/librenms/LibreNMS/service.py</td>
</tr>
<tr>
<td><code>LibreNMS/queuemanager.py</code></td>
<td>Discovery queue manager</td>
<td>/opt/librenms/LibreNMS/queuemanager.py</td>
</tr>
<tr>
<td><code>volumes/librenms/logs/</code></td>
<td>Discovery logs</td>
<td>Host: volumes/librenms/logs/</td>
</tr>
</tbody>
</table>

<h2>Discovery Monitoring</h2>

<h3>1. Checking Logs</h3>

<ac:structured-macro ac:name="code" ac:schema-version="1" ac:macro-id="log-check">
<ac:parameter ac:name="language">bash</ac:parameter>
<ac:parameter ac:name="title">Log Monitoring Commands</ac:parameter>
<ac:plain-text-body><![CDATA[# Recent discovery logs
grep "devices discovered in" volumes/librenms/logs/librenms.log | tail -10

# Real-time monitoring
docker exec librenms tail -f /opt/librenms/logs/librenms.log | grep discovery]]></ac:plain-text-body>
</ac:structured-macro>

<h3>2. Service Status Check</h3>

<ac:structured-macro ac:name="code" ac:schema-version="1" ac:macro-id="service-check">
<ac:parameter ac:name="language">bash</ac:parameter>
<ac:parameter ac:name="title">Service Status Commands</ac:parameter>
<ac:plain-text-body><![CDATA[# Container status
docker ps | grep librenms

# Dispatcher processes
docker exec librenms_dispatcher ps aux | grep python

# Laravel scheduler check
docker exec librenms php artisan schedule:list]]></ac:plain-text-body>
</ac:structured-macro>

<h3>3. Typical Log Messages</h3>

<ac:structured-macro ac:name="code" ac:schema-version="1" ac:macro-id="log-examples">
<ac:parameter ac:name="language">text</ac:parameter>
<ac:parameter ac:name="title">Log Message Examples</ac:parameter>
<ac:plain-text-body><![CDATA[# Successful discovery
/opt/librenms/discovery.php 1 2025-05-30 07:12:02 - 0 devices discovered in 1.027 secs

# Discovery with errors
worker 1 finished device 123 in 45 seconds with exit code 1]]></ac:plain-text-body>
</ac:structured-macro>

<h2>Manual Execution</h2>

<h3>Discovery for All Devices</h3>

<ac:structured-macro ac:name="code" ac:schema-version="1" ac:macro-id="discovery-all">
<ac:parameter ac:name="language">bash</ac:parameter>
<ac:plain-text-body><![CDATA[docker exec librenms php /opt/librenms/discovery.php -h all]]></ac:plain-text-body>
</ac:structured-macro>

<h3>Discovery for Specific Device</h3>

<ac:structured-macro ac:name="code" ac:schema-version="1" ac:macro-id="discovery-device">
<ac:parameter ac:name="language">bash</ac:parameter>
<ac:plain-text-body><![CDATA[docker exec librenms php /opt/librenms/discovery.php -h 1]]></ac:plain-text-body>
</ac:structured-macro>

<h3>Discovery with Debug</h3>

<ac:structured-macro ac:name="code" ac:schema-version="1" ac:macro-id="discovery-debug">
<ac:parameter ac:name="language">bash</ac:parameter>
<ac:plain-text-body><![CDATA[docker exec librenms php /opt/librenms/discovery.php -h 1 -d]]></ac:plain-text-body>
</ac:structured-macro>

<h3>Discovery for Specific Modules</h3>

<ac:structured-macro ac:name="code" ac:schema-version="1" ac:macro-id="discovery-modules">
<ac:parameter ac:name="language">bash</ac:parameter>
<ac:plain-text-body><![CDATA[docker exec librenms php /opt/librenms/discovery.php -h 1 -m ports,sensors]]></ac:plain-text-body>
</ac:structured-macro>

<h2>Troubleshooting</h2>

<ac:structured-macro ac:name="warning" ac:schema-version="1" ac:macro-id="common-issues">
<ac:parameter ac:name="title">Common Issues</ac:parameter>
<ac:rich-text-body>
<ul>
<li><strong>Discovery not running:</strong> Check if dispatcher service is active</li>
<li><strong>Missing logs:</strong> Check permissions on volumes/ directory</li>
<li><strong>Poor performance:</strong> Increase number of workers</li>
<li><strong>Timeouts:</strong> Check SNMP connectivity</li>
</ul>
</ac:rich-text-body>
</ac:structured-macro>

<h3>Diagnostic Commands</h3>

<ac:structured-macro ac:name="code" ac:schema-version="1" ac:macro-id="diagnostic-commands">
<ac:parameter ac:name="language">bash</ac:parameter>
<ac:parameter ac:name="title">Troubleshooting Commands</ac:parameter>
<ac:plain-text-body><![CDATA[# Check discovery configuration
docker exec librenms php artisan config:get | grep discovery

# Check service status
docker exec librenms_dispatcher python3 -c "import LibreNMS; print('Service OK')"

# Test SNMP connectivity
docker exec librenms php /opt/librenms/snmp-scan.py -h 192.168.1.1]]></ac:plain-text-body>
</ac:structured-macro>

<h2>Performance Optimization</h2>

<table>
<tbody>
<tr>
<th>Parameter</th>
<th>Default Value</th>
<th>Recommendation</th>
</tr>
<tr>
<td>service_discovery_workers</td>
<td>16</td>
<td>2-4 for small setups, 8-16 for large setups</td>
</tr>
<tr>
<td>service_discovery_frequency</td>
<td>21600 (6h)</td>
<td>10800 (3h) for dynamic networks</td>
</tr>
<tr>
<td>discovery timeout</td>
<td>900s</td>
<td>Increase for slow devices</td>
</tr>
</tbody>
</table>

<ac:structured-macro ac:name="tip" ac:schema-version="1" ac:macro-id="performance-tip">
<ac:parameter ac:name="title">Performance Tip</ac:parameter>
<ac:rich-text-body>
<p>For setups with many devices, consider using distributed polling with memcached for optimal performance.</p>
</ac:rich-text-body>
</ac:structured-macro>

---

<p><em>Documentation generated for LibreNMS Docker Setup</em></p>
