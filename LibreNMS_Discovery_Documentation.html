<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LibreNMS Discovery - Documentație</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2980b9;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        .info-box {
            background: #e8f4fd;
            border: 1px solid #3498db;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fef9e7;
            border: 1px solid #f39c12;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success-box {
            background: #eafaf1;
            border: 1px solid #27ae60;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .flow-diagram {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .arrow {
            font-size: 24px;
            color: #3498db;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 LibreNMS Discovery - Documentație Completă</h1>
        
        <div class="info-box">
            <strong>📋 Rezumat:</strong> Discovery-ul în LibreNMS este procesul prin care sistemul identifică și colectează informații despre dispozitivele din rețea. Acest proces rulează automat la intervale regulate pentru a menține informațiile actualizate.
        </div>

        <h2>🏗️ Arhitectura Discovery</h2>
        
        <div class="flow-diagram">
            <strong>Fluxul Discovery în Docker Setup</strong><br>
            <div class="arrow">⬇️</div>
            <strong>Dispatcher Service</strong> (librenms_dispatcher)<br>
            <div class="arrow">⬇️</div>
            <strong>DiscoveryQueueManager</strong><br>
            <div class="arrow">⬇️</div>
            <strong>discovery.php</strong> (pentru fiecare dispozitiv)<br>
            <div class="arrow">⬇️</div>
            <strong>Loguri în volumes/librenms/logs/</strong>
        </div>

        <h2>⚙️ Configurarea Discovery</h2>
        
        <h3>1. Tipuri de Scheduler</h3>
        <table>
            <tr>
                <th>Tip</th>
                <th>Descriere</th>
                <th>Utilizare</th>
            </tr>
            <tr>
                <td><span class="highlight">legacy</span></td>
                <td>Folosește dispatcher service sau cron</td>
                <td>Setup-ul curent (recomandat)</td>
            </tr>
            <tr>
                <td><span class="highlight">cron</span></td>
                <td>Folosește doar cron jobs</td>
                <td>Setup-uri simple</td>
            </tr>
            <tr>
                <td><span class="highlight">dispatcher</span></td>
                <td>Folosește doar dispatcher service</td>
                <td>Setup-uri avansate</td>
            </tr>
        </table>

        <h3>2. Configurații Principale</h3>
        <div class="code-block">
# Verificare configurație curentă
docker exec librenms php artisan config:get schedule_type.discovery
# Rezultat: legacy

docker exec librenms php artisan config:get service_discovery_frequency  
# Rezultat: 21600 (6 ore în secunde)

docker exec librenms php artisan config:get service_discovery_workers
# Rezultat: numărul de worker-uri
        </div>

        <h3>3. Modificarea Configurației</h3>
        <div class="code-block">
# Schimbarea frecvenței (în secunde)
docker exec librenms php artisan config:set service_discovery_frequency 10800  # 3 ore

# Schimbarea numărului de worker-uri
docker exec librenms php artisan config:set service_discovery_workers 4

# Schimbarea tipului de scheduler
docker exec librenms php artisan config:set schedule_type.discovery dispatcher
        </div>

        <h2>🐳 Containere Docker Implicate</h2>
        
        <table>
            <tr>
                <th>Container</th>
                <th>Rol</th>
                <th>Fișiere Importante</th>
            </tr>
            <tr>
                <td><strong>librenms_dispatcher</strong></td>
                <td>Execută discovery-ul prin serviciul dispatcher</td>
                <td>/opt/librenms/librenms-service.py</td>
            </tr>
            <tr>
                <td><strong>librenms_cron</strong></td>
                <td>Rulează task-uri programate (Laravel scheduler)</td>
                <td>/var/spool/cron/crontabs/librenms</td>
            </tr>
            <tr>
                <td><strong>librenms</strong></td>
                <td>Container principal cu aplicația</td>
                <td>/opt/librenms/discovery.php</td>
            </tr>
        </table>

        <h2>⏰ Intervalul de Rulare</h2>
        
        <div class="success-box">
            <strong>✅ Interval Implicit:</strong> Discovery-ul rulează la fiecare <span class="highlight">6 ore (21600 secunde)</span>
        </div>

        <h3>Cum se Determină Intervalul</h3>
        <ol>
            <li><strong>Configurația implicită:</strong> 21600 secunde (6 ore) în <code>LibreNMS/service.py</code></li>
            <li><strong>Configurația personalizată:</strong> <code>service_discovery_frequency</code></li>
            <li><strong>Per nod:</strong> Poate fi suprascrisă pentru fiecare nod în parte</li>
        </ol>

        <h2>📁 Fișiere și Locații Importante</h2>
        
        <table>
            <tr>
                <th>Fișier/Director</th>
                <th>Descriere</th>
                <th>Locația în Docker</th>
            </tr>
            <tr>
                <td><code>discovery.php</code></td>
                <td>Script principal de discovery</td>
                <td>/opt/librenms/discovery.php</td>
            </tr>
            <tr>
                <td><code>LibreNMS/service.py</code></td>
                <td>Serviciul dispatcher</td>
                <td>/opt/librenms/LibreNMS/service.py</td>
            </tr>
            <tr>
                <td><code>LibreNMS/queuemanager.py</code></td>
                <td>Manager pentru queue-urile de discovery</td>
                <td>/opt/librenms/LibreNMS/queuemanager.py</td>
            </tr>
            <tr>
                <td><code>volumes/librenms/logs/</code></td>
                <td>Logurile discovery</td>
                <td>Host: volumes/librenms/logs/</td>
            </tr>
        </table>

        <h2>📊 Monitorizarea Discovery</h2>
        
        <h3>1. Verificarea Logurilor</h3>
        <div class="code-block">
# Loguri recente de discovery
grep "devices discovered in" volumes/librenms/logs/librenms.log | tail -10

# Monitorizare în timp real
docker exec librenms tail -f /opt/librenms/logs/librenms.log | grep discovery
        </div>

        <h3>2. Verificarea Statusului Serviciilor</h3>
        <div class="code-block">
# Status containere
docker ps | grep librenms

# Procese în dispatcher
docker exec librenms_dispatcher ps aux | grep python

# Verificare scheduler Laravel
docker exec librenms php artisan schedule:list
        </div>

        <h3>3. Mesaje de Log Tipice</h3>
        <div class="code-block">
# Discovery reușit
/opt/librenms/discovery.php 1 2025-05-30 07:12:02 - 0 devices discovered in 1.027 secs

# Discovery cu erori
worker 1 finished device 123 in 45 seconds with exit code 1
        </div>

        <h2>🔧 Rularea Manuală</h2>
        
        <h3>Discovery pentru Toate Dispozitivele</h3>
        <div class="code-block">
docker exec librenms php /opt/librenms/discovery.php -h all
        </div>

        <h3>Discovery pentru un Dispozitiv Specific</h3>
        <div class="code-block">
docker exec librenms php /opt/librenms/discovery.php -h 1
        </div>

        <h3>Discovery cu Debug</h3>
        <div class="code-block">
docker exec librenms php /opt/librenms/discovery.php -h 1 -d
        </div>

        <h3>Discovery pentru Module Specifice</h3>
        <div class="code-block">
docker exec librenms php /opt/librenms/discovery.php -h 1 -m ports,sensors
        </div>

        <h2>⚠️ Troubleshooting</h2>
        
        <div class="warning-box">
            <h3>Probleme Comune</h3>
            <ul>
                <li><strong>Discovery nu rulează:</strong> Verificați dacă serviciul dispatcher este activ</li>
                <li><strong>Loguri lipsă:</strong> Verificați permisiunile pe directorul volumes/</li>
                <li><strong>Performanță slabă:</strong> Măriți numărul de worker-uri</li>
                <li><strong>Timeout-uri:</strong> Verificați conectivitatea SNMP</li>
            </ul>
        </div>

        <h3>Comenzi de Diagnostic</h3>
        <div class="code-block">
# Verificare configurație discovery
docker exec librenms php artisan config:get | grep discovery

# Verificare status servicii
docker exec librenms_dispatcher python3 -c "import LibreNMS; print('Service OK')"

# Test conectivitate SNMP
docker exec librenms php /opt/librenms/snmp-scan.py -h ***********
        </div>

        <h2>📈 Optimizarea Performanței</h2>
        
        <table>
            <tr>
                <th>Parametru</th>
                <th>Valoare Implicită</th>
                <th>Recomandare</th>
            </tr>
            <tr>
                <td>service_discovery_workers</td>
                <td>16</td>
                <td>2-4 pentru setup-uri mici, 8-16 pentru setup-uri mari</td>
            </tr>
            <tr>
                <td>service_discovery_frequency</td>
                <td>21600 (6h)</td>
                <td>10800 (3h) pentru rețele dinamice</td>
            </tr>
            <tr>
                <td>discovery timeout</td>
                <td>900s</td>
                <td>Măriți pentru dispozitive lente</td>
            </tr>
        </table>

        <div class="info-box">
            <strong>💡 Sfat:</strong> Pentru setup-uri cu multe dispozitive, considerați utilizarea distributed polling cu memcached pentru performanță optimă.
        </div>

        <hr>
        <p style="text-align: center; color: #7f8c8d; margin-top: 30px;">
            <em>Documentație generată pentru LibreNMS Docker Setup - <?php echo date('Y-m-d H:i:s'); ?></em>
        </p>
    </div>
</body>
</html>
